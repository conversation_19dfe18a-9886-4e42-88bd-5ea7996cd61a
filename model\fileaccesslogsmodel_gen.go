// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	fileAccessLogsFieldNames          = builder.RawFieldNames(&FileAccessLogs{}, true)
	fileAccessLogsRows                = strings.Join(fileAccessLogsFieldNames, ",")
	fileAccessLogsRowsExpectAutoSet   = strings.Join(stringx.Remove(fileAccessLogsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	fileAccessLogsRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(fileAccessLogsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	fileAccessLogsModel interface {
		Insert(ctx context.Context, data *FileAccessLogs) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*FileAccessLogs, error)
		Update(ctx context.Context, data *FileAccessLogs) error
		Delete(ctx context.Context, id int64) error
	}

	defaultFileAccessLogsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	FileAccessLogs struct {
		Id         int64          `db:"id"`
		FileId     string         `db:"file_id"`     // 文件ID
		UserId     int64          `db:"user_id"`     // 用户ID
		AccessType int64          `db:"access_type"` // 访问类型：1-查看 2-下载 3-编辑
		AccessTime time.Time      `db:"access_time"` // 访问时间
		IpAddress  sql.NullString `db:"ip_address"`  // 访问IP地址
		UserAgent  sql.NullString `db:"user_agent"`  // 用户代理
	}
)

func newFileAccessLogsModel(conn sqlx.SqlConn) *defaultFileAccessLogsModel {
	return &defaultFileAccessLogsModel{
		conn:  conn,
		table: `"public"."file_access_logs"`,
	}
}

func (m *defaultFileAccessLogsModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultFileAccessLogsModel) FindOne(ctx context.Context, id int64) (*FileAccessLogs, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", fileAccessLogsRows, m.table)
	var resp FileAccessLogs
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFileAccessLogsModel) Insert(ctx context.Context, data *FileAccessLogs) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6)", m.table, fileAccessLogsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.FileId, data.UserId, data.AccessType, data.AccessTime, data.IpAddress, data.UserAgent)
	return ret, err
}

func (m *defaultFileAccessLogsModel) Update(ctx context.Context, data *FileAccessLogs) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, fileAccessLogsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Id, data.FileId, data.UserId, data.AccessType, data.AccessTime, data.IpAddress, data.UserAgent)
	return err
}

func (m *defaultFileAccessLogsModel) tableName() string {
	return m.table
}
