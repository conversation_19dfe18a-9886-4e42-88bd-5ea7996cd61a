package disk

import (
	"context"
	"errors"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
	"paper-editor-api/internal/svc"
)

type DeleteFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除文件
func NewDeleteFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteFileLogic {
	return &DeleteFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// DeleteFile 删除文件
// 参数:
//
//	无显式参数
//
// 返回值:
//
//	resp bool - 删除操作是否成功
//	err error - 错误信息，如果操作成功则为nil
func (l *DeleteFileLogic) DeleteFile() (resp bool, err error) {
	fileId, ok := l.ctx.Value("fileId").(string)
	if !ok {
		return false, errors.New("获取用户信息失败")
	}
	coverfileId, err := strconv.ParseInt(fileId, 10, 64)
	err = l.svcCtx.FilesModel.Delete(l.ctx, coverfileId)
	if err != nil {
		return false, errors.New("删除文件失败")
	}

	return true, nil
}
