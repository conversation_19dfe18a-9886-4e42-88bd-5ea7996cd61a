# Docker 环境使用指南

本文档介绍如何使用 Docker Compose 启动论文编辑器 API 服务的开发环境。

## 环境要求

- Docker Engine 20.10.0+
- Docker Compose 2.0.0+
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

## 包含的服务

Docker Compose 配置包含以下服务：

1. **PostgreSQL** - 关系型数据库，用于存储结构化数据
2. **Elasticsearch** - 搜索引擎，用于全文搜索和向量检索
3. **Kibana** - Elasticsearch 的管理界面
4. **Redis** - 缓存服务
5. **MinIO** - 对象存储服务，用于文件存储
6. **Init Service** - 初始化服务，用于设置 Elasticsearch 索引和 MinIO 存储桶

## 快速开始

### 启动所有服务

```bash
cd paper-editor-api
docker-compose up -d
```

### 查看服务状态

```bash
docker-compose ps
```

### 查看服务日志

```bash
# 查看所有服务的日志
docker-compose logs

# 查看特定服务的日志
docker-compose logs postgres
docker-compose logs elasticsearch
```

### 停止所有服务

```bash
docker-compose down
```

### 停止并删除所有数据

```bash
docker-compose down -v
```

## 服务访问

启动所有服务后，可以通过以下地址访问各个服务：

- **PostgreSQL**: localhost:5432
  - 用户名: postgres
  - 密码: password
  - 数据库: paper_editor

- **Elasticsearch**: http://localhost:9200
  - 用户名: elastic
  - 密码: changeme

- **Kibana**: http://localhost:5601
  - 用户名: elastic
  - 密码: changeme

- **Redis**: localhost:6379
  - 无密码

- **MinIO**: 
  - API: http://localhost:9000
  - 控制台: http://localhost:9001
  - 用户名: minioadmin
  - 密码: minioadmin

## 初始化数据

PostgreSQL 数据库会在首次启动时自动执行 `sql/schema.sql` 脚本，创建所需的表结构和初始数据。

Elasticsearch 索引会由 init-service 服务自动创建。

## 配置应用连接

要将应用程序连接到这些服务，请使用以下配置：

### PostgreSQL 连接字符串

```
host=localhost port=5432 user=postgres password=password dbname=paper_editor sslmode=disable
```

### Elasticsearch 连接

```yaml
Elasticsearch:
  Addresses:
    - http://localhost:9200
  Username: elastic
  Password: changeme
  Indices:
    Knowledge: knowledge_vectors
```

### Redis 连接

```yaml
Cache:
  - Host: localhost:6379
    Pass: 
    Type: node
```

### MinIO 连接

```yaml
FileStorage:
  Type: minio
  Endpoint: localhost:9000
  AccessKey: minioadmin
  SecretKey: minioadmin
  Bucket: paper-editor
  UseSSL: false
```

## 常见问题

### Elasticsearch 启动失败

如果 Elasticsearch 启动失败，可能是因为系统的 vm.max_map_count 值太低。可以通过以下命令增加该值：

```bash
# Linux
sudo sysctl -w vm.max_map_count=262144

# Windows (WSL)
wsl -d docker-desktop sysctl -w vm.max_map_count=262144
```

### MinIO 存储桶创建

首次启动后，需要通过 MinIO 控制台创建存储桶。访问 http://localhost:9001，使用上述凭据登录，然后创建名为 `paper-editor` 的存储桶。

### 数据持久化

所有服务的数据都存储在 Docker 卷中，即使容器被删除，数据也会保留。如果要完全删除数据，请使用 `docker-compose down -v` 命令。