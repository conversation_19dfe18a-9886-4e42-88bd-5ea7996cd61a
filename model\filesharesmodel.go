package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ FileSharesModel = (*customFileSharesModel)(nil)

type (
	// FileSharesModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFileSharesModel.
	FileSharesModel interface {
		fileSharesModel
		withSession(session sqlx.Session) FileSharesModel
	}

	customFileSharesModel struct {
		*defaultFileSharesModel
	}
)

// NewFileSharesModel returns a model for the database table.
func NewFileSharesModel(conn sqlx.SqlConn) FileSharesModel {
	return &customFileSharesModel{
		defaultFileSharesModel: newFileSharesModel(conn),
	}
}

func (m *customFileSharesModel) withSession(session sqlx.Session) FileSharesModel {
	return NewFileSharesModel(sqlx.NewSqlConnFromSession(session))
}
