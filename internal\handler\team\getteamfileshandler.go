package team

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/team"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 获取团队文件列表
func GetTeamFilesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.TeamFileListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := team.NewGetTeamFilesLogic(r.Context(), svcCtx)
		resp, err := l.GetTeamFiles(&req)
		response.Response(w, resp, err)
	}
}
