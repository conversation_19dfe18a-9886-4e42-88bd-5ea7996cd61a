package team

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type InviteTeamMemberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 邀请团队成员
func NewInviteTeamMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *InviteTeamMemberLogic {
	return &InviteTeamMemberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *InviteTeamMemberLogic) InviteTeamMember(req *types.InviteTeamMemberReq) (resp *types.BaseResp, err error) {
	// todo: add your logic here and delete this line

	return
}
