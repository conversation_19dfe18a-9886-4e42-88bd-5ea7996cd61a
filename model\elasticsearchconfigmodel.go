package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ ElasticsearchConfigModel = (*customElasticsearchConfigModel)(nil)

type (
	// ElasticsearchConfigModel is an interface to be customized, add more methods here,
	// and implement the added methods in customElasticsearchConfigModel.
	ElasticsearchConfigModel interface {
		elasticsearchConfigModel
		withSession(session sqlx.Session) ElasticsearchConfigModel
	}

	customElasticsearchConfigModel struct {
		*defaultElasticsearchConfigModel
	}
)

// NewElasticsearchConfigModel returns a model for the database table.
func NewElasticsearchConfigModel(conn sqlx.SqlConn) ElasticsearchConfigModel {
	return &customElasticsearchConfigModel{
		defaultElasticsearchConfigModel: newElasticsearchConfigModel(conn),
	}
}

func (m *customElasticsearchConfigModel) withSession(session sqlx.Session) ElasticsearchConfigModel {
	return NewElasticsearchConfigModel(sqlx.NewSqlConnFromSession(session))
}
