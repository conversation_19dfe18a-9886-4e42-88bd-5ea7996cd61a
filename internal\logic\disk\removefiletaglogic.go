package disk

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RemoveFileTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 移除文件标签
func NewRemoveFileTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveFileTagLogic {
	return &RemoveFileTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RemoveFileTagLogic) RemoveFileTag() (resp *types.BaseResp, err error) {
	// todo: add your logic here and delete this line

	return
}
