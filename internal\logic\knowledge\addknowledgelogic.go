package knowledge

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/model"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddKnowledgeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 添加知识点
func NewAddKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddKnowledgeLogic {
	return &AddKnowledgeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddKnowledgeLogic) AddKnowledge(req *types.AddKnowledgeReq) (*types.AddKnowledgeResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 生成知识点ID
	knowledgeId := fmt.Sprintf("k_%s", uuid.New().String()[:8])

	// 创建知识点
	k := &model.Knowledge{
		KnowledgeId: knowledgeId,
		Title:       req.Title,
		Content:     req.Content,
		CategoryId:  sql.NullInt64{Int64: req.CategoryId, Valid: req.CategoryId > 0},
		UserId:      userId,
		Status:      1, // 正常状态
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
	}

	// 插入知识点
	_, err := l.svcCtx.KnowledgeModel.Insert(context.Background(), k)
	if err != nil {
		return nil, errors.New("添加知识点失败: " + err.Error())
	}

	// 如果有标签，添加标签关联
	if len(req.Tags) > 0 {
		// 这里简化处理，实际应该先查询或创建标签，然后建立关联
		// 此处仅为示例
	}

	return &types.AddKnowledgeResp{
		KnowledgeId: knowledgeId,
	}, nil
}
