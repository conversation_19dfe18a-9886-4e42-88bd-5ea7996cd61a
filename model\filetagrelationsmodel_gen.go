// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	fileTagRelationsFieldNames          = builder.RawFieldNames(&FileTagRelations{}, true)
	fileTagRelationsRows                = strings.Join(fileTagRelationsFieldNames, ",")
	fileTagRelationsRowsExpectAutoSet   = strings.Join(stringx.Remove(fileTagRelationsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	fileTagRelationsRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(fileTagRelationsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	fileTagRelationsModel interface {
		Insert(ctx context.Context, data *FileTagRelations) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*FileTagRelations, error)
		Update(ctx context.Context, data *FileTagRelations) error
		Delete(ctx context.Context, id int64) error
	}

	defaultFileTagRelationsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	FileTagRelations struct {
		Id         int64     `db:"id"`
		FileId     string    `db:"file_id"`
		TagId      int64     `db:"tag_id"`
		UserId     int64     `db:"user_id"`
		CreateTime time.Time `db:"create_time"`
		TagName    string    `db:"tag_name"` // 标签名称，用于联合查询
	}
)

func newFileTagRelationsModel(conn sqlx.SqlConn) *defaultFileTagRelationsModel {
	return &defaultFileTagRelationsModel{
		conn:  conn,
		table: `"public"."file_tag_relations"`,
	}
}

func (m *defaultFileTagRelationsModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultFileTagRelationsModel) FindOne(ctx context.Context, id int64) (*FileTagRelations, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", fileTagRelationsRows, m.table)
	var resp FileTagRelations
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFileTagRelationsModel) Insert(ctx context.Context, data *FileTagRelations) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3)", m.table, fileTagRelationsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.FileId, data.TagId, data.UserId)
	return ret, err
}

func (m *defaultFileTagRelationsModel) Update(ctx context.Context, data *FileTagRelations) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, fileTagRelationsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Id, data.FileId, data.TagId, data.UserId)
	return err
}

func (m *defaultFileTagRelationsModel) tableName() string {
	return m.table
}
