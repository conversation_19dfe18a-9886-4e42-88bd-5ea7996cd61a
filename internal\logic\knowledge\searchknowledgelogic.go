package knowledge

import (
	"context"
	"errors"
	"paper-editor-api/internal/middleware"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SearchKnowledgeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 搜索知识点
func NewSearchKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchKnowledgeLogic {
	return &SearchKnowledgeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SearchKnowledgeLogic) SearchKnowledge(req *types.SearchKnowledgeReq) (*types.SearchKnowledgeResp, error) {
	// 从上下文中获取用户ID
	_, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// TODO: 实现搜索功能
	// 由于 goctl 生成的模型没有 Search 方法，这里需要自定义实现
	// 临时返回空结果
	items := []types.KnowledgeInfo{}

	return &types.SearchKnowledgeResp{
		Total: 0,
		Items: items,
	}, nil
}
