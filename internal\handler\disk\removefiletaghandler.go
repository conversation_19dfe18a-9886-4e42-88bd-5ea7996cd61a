package disk

import (
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/disk"
	"paper-editor-api/internal/svc"
)

// 移除文件标签
func RemoveFileTagHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := disk.NewRemoveFileTagLogic(r.Context(), svcCtx)
		resp, err := l.RemoveFileTag()
		response.Response(w, resp, err)
	}
}
