package knowledge

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteKnowledgeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除知识点
func NewDeleteKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteKnowledgeLogic {
	return &DeleteKnowledgeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteKnowledgeLogic) DeleteKnowledge() (resp *types.BaseResp, err error) {
	// todo: add your logic here and delete this line

	return
}
