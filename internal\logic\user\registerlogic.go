package user

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"paper-editor-api/model"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户注册
func NewRegisterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterLogic {
	return &RegisterLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RegisterLogic) Register(req *types.RegisterReq) (*types.RegisterResp, error) {
	// 1. 检查用户名是否已存在
	existingUser, err := l.svcCtx.UsersModel.FindOneByUsername(context.Background(), req.Username)
	if err == nil && existingUser != nil {
		return nil, errors.New("用户名已存在")
	}

	// 2. 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, errors.New("密码加密失败")
	}

	// 3. 生成用户ID
	userId := fmt.Sprintf("user_%s", uuid.New().String()[:8])

	// 4. 创建用户记录
	now := time.Now()
	user := &model.Users{
		UserId:           userId,
		Username:         req.Username,
		Password:         string(hashedPassword),
		Nickname:         sql.NullString{String: req.Nickname, Valid: req.Nickname != ""},
		Email:            sql.NullString{String: req.Email, Valid: req.Email != ""},
		MembershipTypeId: 1,          // 默认为普通用户
		StorageQuota:     1073741824, // 默认1GB存储配额
		KnowledgeQuota:   100,        // 默认100个知识点配额
		Status:           1,          // 正常状态
		CreateTime:       now,
		UpdateTime:       now,
	}

	// 5. 插入用户记录
	_, err = l.svcCtx.UsersModel.Insert(context.Background(), user)
	if err != nil {
		return nil, errors.New("用户注册失败: " + err.Error())
	}

	return &types.RegisterResp{
		UserId: userId,
	}, nil
}
