package team

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UploadTeamFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 上传团队文件
func NewUploadTeamFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadTeamFileLogic {
	return &UploadTeamFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UploadTeamFileLogic) UploadTeamFile() (resp *types.FileInfo, err error) {
	// todo: add your logic here and delete this line

	return
}
