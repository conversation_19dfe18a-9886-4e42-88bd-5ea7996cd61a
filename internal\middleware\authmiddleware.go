package middleware

import (
	"context"
	"encoding/json"
	"net/http"
	"paper-editor-api/internal/config"
	"strings"

	"github.com/golang-jwt/jwt/v4"
)

// 用户信息上下文键
const (
	UserIdKey   = "userId"
	UserNameKey = "userName"
	UserRoleKey = "userRole"
)

// JWT声明结构
type Claims struct {
	UserId   int64  `json:"userId"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

type AuthMiddleware struct {
	Config config.Config
}

func NewAuthMiddleware(c config.Config) *AuthMiddleware {
	return &AuthMiddleware{
		Config: c,
	}
}

/* <<<<<<<<<<<<<<  ✨ Windsurf Command ⭐ >>>>>>>>>>>>>>>> */
// Handle 是一个HTTP中间件,用于验证并解析 Authorization 头中的 Bearer 令牌,
// 并将解析后的用户信息添加到请求上下文中,以便后续处理器可以使用。
//
// 对于无效的令牌,它将返回 401 状态码,并将响应体设置为 JSON 格式的错误信息。
// 对于有效的令牌,它将继续处理请求,并将用户信息添加到上下文中。
/* <<<<<<<<<<  4318bf49-518a-4e50-bb7f-8a42bdd9b106  >>>>>>>>>>> */
func (m *AuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从请求头获取Token
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			responseUnauthorized(w, "未提供认证令牌")
			return
		}

		// 提取Bearer Token
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			responseUnauthorized(w, "认证令牌格式错误")
			return
		}
		tokenString := parts[1]

		// 解析Token
		token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
			return []byte(m.Config.Auth.AccessSecret), nil
		})

		if err != nil {
			responseUnauthorized(w, "无效的认证令牌")
			return
		}

		// 验证Token
		if claims, ok := token.Claims.(*Claims); ok && token.Valid {
			// 将用户信息添加到请求上下文
			ctx := context.WithValue(r.Context(), UserIdKey, claims.UserId)
			ctx = context.WithValue(ctx, UserNameKey, claims.Username)
			ctx = context.WithValue(ctx, UserRoleKey, claims.Role)

			// 使用新的上下文继续处理请求
			next(w, r.WithContext(ctx))
		} else {
			responseUnauthorized(w, "认证令牌已过期或无效")
		}
	}
}

// 返回未授权响应
func responseUnauthorized(w http.ResponseWriter, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusUnauthorized)

	response := map[string]interface{}{
		"code":    401,
		"message": message,
	}

	json.NewEncoder(w).Encode(response)
}
