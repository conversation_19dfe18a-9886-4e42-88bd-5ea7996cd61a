package user

import (
	"context"
	"errors"
	"paper-editor-api/internal/middleware"

	"github.com/zeromicro/go-zero/core/logx"
	"paper-editor-api/internal/svc"
)

type DeleteAccountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除账户
func NewDeleteAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteAccountLogic {
	return &DeleteAccountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteAccountLogic) DeleteAccount() (resp bool, err error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return false, errors.New("获取用户信息失败")
	}

	// 删除对应 Id 账户
	err = l.svcCtx.UsersModel.Delete(l.ctx, userId)
	if err != nil {
		return false, errors.New("删除账户失败，请联系管理员")
	}

	return true, nil
}
