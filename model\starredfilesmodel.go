package model

import (
	"context"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ StarredFilesModel = (*customStarredFilesModel)(nil)

type (
	// StarredFilesModel is an interface to be customized, add more methods here,
	// and implement the added methods in customStarredFilesModel.
	StarredFilesModel interface {
		starredFilesModel
		withSession(session sqlx.Session) StarredFilesModel
		FindByFileIdAndUserId(ctx context.Context, fileId string, userId int64) (*StarredFiles, error)
		FindByUserId(ctx context.Context, userId int64, limit int, page_size int) ([]*StarredFiles, error)
		CountByUserId(ctx context.Context, userId int64) (int64, error)
	}

	customStarredFilesModel struct {
		*defaultStarredFilesModel
	}
)

// NewStarredFilesModel returns a model for the database table.
func NewStarredFilesModel(conn sqlx.SqlConn) StarredFilesModel {
	return &customStarredFilesModel{
		defaultStarredFilesModel: newStarredFilesModel(conn),
	}
}

func (m *customStarredFilesModel) withSession(session sqlx.Session) StarredFilesModel {
	return NewStarredFilesModel(sqlx.NewSqlConnFromSession(session))
}

// FindByFileIdAndUserId 根据文件ID和用户ID查找收藏的文件记录
// 参数:
//
//	ctx - 上下文对象，用于控制请求的生命周期
//	fileId - 文件ID
//	userId - 用户ID
//
// 返回值:
//
//	*StarredFiles - 找到的收藏文件记录指针，如果未找到则为nil
//	error - 错误信息，如果查找成功则为nil，未找到记录时返回ErrNotFound
func (m *customStarredFilesModel) FindByFileIdAndUserId(ctx context.Context, fileId string, userId int64) (*StarredFiles, error) {
	// 构造SQL查询语句，根据file_id和user_id精确查找一条记录
	query := fmt.Sprintf("select %s from %s where file_id = $1 and user_id = $2 limit 1", starredFilesRows, m.table)
	var resp StarredFiles
	err := m.conn.QueryRowCtx(ctx, &resp, query, fileId, userId)
	switch {
	case err == nil:
		return &resp, nil
	case errors.Is(err, sqlx.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

// FindByUserId 根据用户ID查询收藏的文件列表
// 参数:
//
//	ctx: 上下文对象，用于控制请求的生命周期
//	userId: 用户ID，用于筛选该用户的收藏文件
//	limit: 限制返回的记录数量
//	pageSize: 分页偏移量，用于分页查询
//
// 返回值:
//
//	[]*StarredFiles: 收藏文件列表的指针切片
//	error: 查询过程中发生的错误，如果查询成功则为nil
func (m *customStarredFilesModel) FindByUserId(ctx context.Context, userId int64, limit int, pageSize int) ([]*StarredFiles, error) {
	// 构造SQL查询语句，根据用户ID筛选并支持分页
	query := fmt.Sprintf("select %s from %s where user_id = $1 limit $2 offset $3", starredFilesRows, m.table)
	var resp []*StarredFiles
	// 执行数据库查询操作
	err := m.conn.QueryRowsCtx(ctx, &resp, query, userId, limit, pageSize)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// CountByUserId 根据用户ID统计收藏文件的数量
// 参数:
//
//	ctx - 上下文对象，用于控制请求的生命周期
//	userId - 用户ID，用于筛选指定用户的收藏文件
//
// 返回值:
//
//	int64 - 指定用户收藏文件的数量
//	error - 执行过程中可能发生的错误
func (m *customStarredFilesModel) CountByUserId(ctx context.Context, userId int64) (int64, error) {
	// 构造查询SQL，根据用户ID筛选收藏文件
	query := fmt.Sprintf("select %s from %s where user_id = $1", starredFilesRows, m.table)
	var resp []*StarredFiles
	// 执行数据库查询操作
	err := m.conn.QueryRowCtx(ctx, &resp, query, userId)
	if err != nil {
		return 0, err
	}
	// 返回查询结果的数量
	return int64(len(resp)), err
}
