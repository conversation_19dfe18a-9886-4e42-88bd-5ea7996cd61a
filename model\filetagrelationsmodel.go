package model

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ FileTagRelationsModel = (*customFileTagRelationsModel)(nil)

type (
	// FileTagRelationsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFileTagRelationsModel.
	FileTagRelationsModel interface {
		fileTagRelationsModel
		withSession(session sqlx.Session) FileTagRelationsModel

		FindByTagId(ctx context.Context, tagId int64) ([]FileTagRelations, error)
		FindByFileAndTagID(ctx context.Context, fileId string, tagId int64) (*FileTagRelations, error)

		FindTagsByFileId(ctx context.Context, fileId string) ([]FileTagRelations, error)
	}

	customFileTagRelationsModel struct {
		*defaultFileTagRelationsModel
	}
)

// NewFileTagRelationsModel returns a model for the database table.
func NewFileTagRelationsModel(conn sqlx.SqlConn) FileTagRelationsModel {
	return &customFileTagRelationsModel{
		defaultFileTagRelationsModel: newFileTagRelationsModel(conn),
	}
}

func (m *customFileTagRelationsModel) withSession(session sqlx.Session) FileTagRelationsModel {
	return NewFileTagRelationsModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customFileTagRelationsModel) FindByTagId(ctx context.Context, tagId int64) ([]FileTagRelations, error) {
	query := fmt.Sprintf("select %s from %s where tag_id = ?", fileTagRelationsRows, m.table)
	var resp []FileTagRelations
	err := m.conn.QueryRowsCtx(ctx, &resp, query, tagId)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// FindByFileAndTagID 根据文件ID和标签ID查找关联关系
func (m *customFileTagRelationsModel) FindByFileAndTagID(ctx context.Context, fileId string, tagId int64) (*FileTagRelations, error) {
	query := fmt.Sprintf("select %s from %s where file_id = ? and tag_id = ? limit 1", fileTagRelationsRows, m.table)
	var resp FileTagRelations
	err := m.conn.QueryRowCtx(ctx, &resp, query, fileId, tagId)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

func (m *customFileTagRelationsModel) FindTagsByFileId(ctx context.Context, fileId string) ([]*FileTag, error) {
	var resp []*FileTag
	query := fmt.Sprintf("SELECT t.* FROM %s ft JOIN file_tags t ON ft.tag_id = t.id WHERE ft.file_id = ?", m.table)
	err := m.conn.QueryRowsCtx(ctx, &resp, query, fileId)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
