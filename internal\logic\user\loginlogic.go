package user

import (
	"context"
	"errors"
	"github.com/golang-jwt/jwt/v4"
	"golang.org/x/crypto/bcrypt"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type LoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户登录
func NewLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginLogic {
	return &LoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LoginLogic) Login(req *types.LoginReq) (*types.LoginResp, error) {
	// 1. 根据用户名查询用户
	user, err := l.svcCtx.UsersModel.FindOneByUsername(context.Background(), req.Userna<PERSON>)
	if err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 2. 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password))
	if err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 3. 获取用户角色
	membershipType, err := l.svcCtx.MembershipTypesModel.FindOne(context.Background(), user.MembershipTypeId)
	if err != nil {
		return nil, errors.New("获取用户信息失败")
	}

	// 4. 生成JWT令牌
	now := time.Now()
	accessExpire := l.svcCtx.Config.Auth.AccessExpire
	accessToken, err := l.generateToken(user.Id, user.Username, membershipType.Name, now, accessExpire)
	if err != nil {
		return nil, err
	}

	// 5. 生成刷新令牌（有效期更长）
	refreshExpire := accessExpire * 7 // 刷新令牌有效期为访问令牌的7倍
	refreshToken, err := l.generateToken(user.Id, user.Username, membershipType.Name, now, refreshExpire)
	if err != nil {
		return nil, err
	}

	return &types.LoginResp{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		Expires:      now.Unix() + accessExpire,
	}, nil
}

// 生成JWT令牌
func (l *LoginLogic) generateToken(userId int64, username, role string, now time.Time, expire int64) (string, error) {
	claims := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"userId":   userId,
		"username": username,
		"role":     role,
		"exp":      now.Unix() + expire,
		"iat":      now.Unix(),
	})

	return claims.SignedString([]byte(l.svcCtx.Config.Auth.AccessSecret))
}
