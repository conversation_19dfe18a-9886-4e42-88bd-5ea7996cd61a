package user

import (
	"context"
	"errors"
	"paper-editor-api/internal/middleware"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户信息
func NewGetUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserInfoLogic {
	return &GetUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserInfoLogic) GetUserInfo() (*types.UserInfoResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 查询用户信息
	user, err := l.svcCtx.UsersModel.FindOne(context.Background(), userId)
	if err != nil {
		return nil, errors.New("用户不存在")
	}

	// 处理可能为空的字段
	nickname := ""
	if user.Nickname.Valid {
		nickname = user.Nickname.String
	}

	avatar := ""
	if user.Avatar.Valid {
		avatar = user.Avatar.String
	}

	email := ""
	if user.Email.Valid {
		email = user.Email.String
	}

	// 获取会员类型信息
	membershipType, err := l.svcCtx.MembershipTypesModel.FindOne(context.Background(), user.MembershipTypeId)
	if err != nil {
		l.Logger.Errorf("获取会员类型失败: %v", err)
	}

	membershipTypeName := "普通用户"
	if membershipType != nil {
		membershipTypeName = membershipType.Name
	}

	phone := ""
	if user.Phone.Valid {
		phone = user.Phone.String
	}

	// 返回用户信息
	return &types.UserInfoResp{
		UserId:         user.UserId,
		Username:       user.Username,
		Nickname:       nickname,
		Avatar:         avatar,
		Email:          email,
		Phone:          phone,
		MembershipType: membershipTypeName,
		StorageQuota:   user.StorageQuota,
		KnowledgeQuota: user.KnowledgeQuota,
		CreateTime:     user.CreateTime.Format(time.DateTime),
	}, nil
}
