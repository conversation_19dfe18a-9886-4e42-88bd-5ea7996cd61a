package team

import (
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/team"
	"paper-editor-api/internal/svc"
)

// 上传团队文件
func UploadTeamFileHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := team.NewUploadTeamFileLogic(r.Context(), svcCtx)
		resp, err := l.UploadTeamFile()
		response.Response(w, resp, err)
	}
}
