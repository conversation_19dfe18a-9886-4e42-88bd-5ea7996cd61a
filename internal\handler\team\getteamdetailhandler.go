package team

import (
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/team"
	"paper-editor-api/internal/svc"
)

// 获取团队详情
func GetTeamDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := team.NewGetTeamDetailLogic(r.Context(), svcCtx)
		resp, err := l.GetTeamDetail()
		response.Response(w, resp, err)
	}
}
