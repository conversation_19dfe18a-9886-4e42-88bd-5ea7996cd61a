package team

import (
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/team"
	"paper-editor-api/internal/svc"
)

// 移除团队成员
func RemoveTeamMemberHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := team.NewRemoveTeamMemberLogic(r.Context(), svcCtx)
		resp, err := l.RemoveTeamMember()
		response.Response(w, resp, err)
	}
}
