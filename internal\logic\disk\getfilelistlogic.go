package disk

import (
	"context"
	"errors"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/model"
	"strconv"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFileListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取文件列表
func NewGetFileListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFileListLogic {
	return &GetFileListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetFileList 获取指定文件夹下的文件和文件夹列表，支持分页。
// 参数:
//   - req: 包含请求参数的结构体，如 FolderId（文件夹ID）、Page（页码）、PageSize（每页数量）等。
//
// 返回值:
//   - *types.FileListResp: 包含文件和文件夹列表及其统计信息的响应结构体。
//   - error: 如果在查询过程中发生错误，则返回相应的错误信息。
func (l *GetFileListLogic) GetFileList(req *types.FileListReq) (*types.FileListResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	var parentId string
	if req.FolderId == "" {
		// 如果 folder_id 为空，则查询根目录
		// 根目录的 parent_id 通常为特定值，例如 0 或空字符串，具体取决于数据库设计
		parentId = "0"
	} else {
		parentId = req.FolderId
	}

	// 查询文件
	files, err := l.svcCtx.FilesModel.FindAllByFolderId(l.ctx, userId, parentId, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	// 查询文件夹
	folders, err := l.svcCtx.FoldersModel.FindAllByParentId(l.ctx, userId, parentId, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	// 获取文件和文件夹总数
	fileCount, err := l.svcCtx.FilesModel.CountByFolderId(l.ctx, userId, parentId)
	if err != nil {
		return nil, err
	}
	folderCount, err := l.svcCtx.FoldersModel.CountByParentId(l.ctx, userId, parentId)
	if err != nil {
		return nil, err
	}

	// 转换文件信息
	fileInfos := make([]types.FileInfo, len(files))
	for i, file := range files {
		// 查询文件是否被收藏
		starred, err := l.svcCtx.StarredFilesModel.FindByFileIdAndUserId(l.ctx, file.FileId, userId)
		var isStarred bool
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			return nil, err
		}
		isStarred = starred != nil

		// 查询文件的标签
		tags, err := l.svcCtx.FileTagRelationsModel.FindTagsByFileId(l.ctx, file.FileId)
		if err != nil {
			return nil, err
		}
		var tagNames []string
		for _, tag := range tags {
			tagNames = append(tagNames, tag.TagName)
		}

		fileInfos[i] = types.FileInfo{
			FileId:     file.FileId,
			FileName:   file.FileName,
			FileSize:   file.FileSize,
			FileType:   file.FileType,
			UploadTime: file.CreateTime.Format(time.DateTime),
			UpdateTime: file.UpdateTime.Format(time.DateTime),
			FolderId:   file.FolderId.String,
			IsStarred:  isStarred,
			Tags:       tagNames,
			Status:     int(file.Status),
		}
	}

	// 转换文件夹信息
	folderInfos := make([]types.FolderInfo, len(folders))
	for i, folder := range folders {
		folderInfos[i] = types.FolderInfo{
			FolderId:   folder.FolderId,
			FolderName: folder.FolderName,
			ParentId:   folder.ParentId.String,
			CreateTime: folder.CreateTime.Format(time.DateTime),
			UpdateTime: folder.UpdateTime.Format(time.DateTime),
			Status:     strconv.FormatInt(folder.Status, 10),
		}
	}

	return &types.FileListResp{
		Total:       fileCount + folderCount,
		Files:       fileInfos,
		Folders:     folderInfos,
		FileCount:   fileCount,
		FolderCount: folderCount,
	}, nil
}
