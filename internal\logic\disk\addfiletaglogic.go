package disk

import (
	"context"
	"strconv"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
	"paper-editor-api/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddFileTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 为文件添加标签
func NewAddFileTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddFileTagLogic {
	return &AddFileTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AddFileTag 为指定文件添加标签
// 参数:
//   - req: 包含文件ID和标签ID的请求参数
//
// 返回值:
//   - resp: 基础响应结构，包含操作结果状态码和消息
//   - err: 错误信息，如果发生系统错误则返回具体错误
func (l *AddFileTagLogic) AddFileTag(req *types.AddFileTagReq) (resp *types.BaseResp, err error) {
	// 初始化响应
	resp = &types.BaseResp{
		Code:    0,
		Message: "添加标签成功",
	}

	fileId, _ := strconv.ParseInt(req.FileId, 10, 64)

	// 1. 检查文件是否存在
	file, err := l.svcCtx.FilesModel.FindOne(l.ctx, fileId)
	if err != nil {
		resp.Code = 1
		resp.Message = "文件不存在"
		return resp, nil
	}

	// 2. 检查标签是否存在
	_, err = l.svcCtx.FileTagsModel.FindOne(l.ctx, req.TagId)
	if err != nil {
		resp.Code = 2
		resp.Message = "标签不存在"
		return resp, nil
	}

	// 3. 检查用户是否有权限操作该文件
	userId := l.ctx.Value("userId").(int64)
	if file.UserId != userId {
		resp.Code = 3
		resp.Message = "您没有权限为此文件添加标签"
		return resp, nil
	}

	// 4. 检查文件和标签的关联是否已存在
	exists, err := l.svcCtx.FileTagRelationsModel.FindByFileAndTagID(l.ctx, req.FileId, req.TagId)
	if err == nil && exists.Id > 0 {
		// 关系已存在，直接返回成功
		return resp, nil
	}

	// 5. 创建文件和标签的关联
	relation := &model.FileTagRelations{
		FileId:     req.FileId,
		TagId:      req.TagId,
		UserId:     userId,
		CreateTime: time.Now(),
	}

	_, err = l.svcCtx.FileTagRelationsModel.Insert(l.ctx, relation)
	if err != nil {
		resp.Code = 4
		resp.Message = "添加标签失败: " + err.Error()
		return resp, nil
	}

	return resp, nil
}
