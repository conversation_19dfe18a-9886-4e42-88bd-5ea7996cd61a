package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ MembershipTypesModel = (*customMembershipTypesModel)(nil)

type (
	// MembershipTypesModel is an interface to be customized, add more methods here,
	// and implement the added methods in customMembershipTypesModel.
	MembershipTypesModel interface {
		membershipTypesModel
		withSession(session sqlx.Session) MembershipTypesModel
	}

	customMembershipTypesModel struct {
		*defaultMembershipTypesModel
	}
)

// NewMembershipTypesModel returns a model for the database table.
func NewMembershipTypesModel(conn sqlx.SqlConn) MembershipTypesModel {
	return &customMembershipTypesModel{
		defaultMembershipTypesModel: newMembershipTypesModel(conn),
	}
}

func (m *customMembershipTypesModel) withSession(session sqlx.Session) MembershipTypesModel {
	return NewMembershipTypesModel(sqlx.NewSqlConnFromSession(session))
}
