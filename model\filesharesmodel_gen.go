// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	fileSharesFieldNames          = builder.RawFieldNames(&FileShares{}, true)
	fileSharesRows                = strings.Join(fileSharesFieldNames, ",")
	fileSharesRowsExpectAutoSet   = strings.Join(stringx.Remove(fileSharesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	fileSharesRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(fileSharesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	fileSharesModel interface {
		Insert(ctx context.Context, data *FileShares) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*FileShares, error)
		FindOneByShareId(ctx context.Context, shareId string) (*FileShares, error)
		Update(ctx context.Context, data *FileShares) error
		Delete(ctx context.Context, id int64) error
	}

	defaultFileSharesModel struct {
		conn  sqlx.SqlConn
		table string
	}

	FileShares struct {
		Id          int64        `db:"id"`
		ShareId     string       `db:"share_id"`
		FileId      string       `db:"file_id"`
		UserId      int64        `db:"user_id"`
		ExpireTime  sql.NullTime `db:"expire_time"`
		AccessCount int64        `db:"access_count"`
		Status      int64        `db:"status"`
		CreateTime  time.Time    `db:"create_time"`
	}
)

func newFileSharesModel(conn sqlx.SqlConn) *defaultFileSharesModel {
	return &defaultFileSharesModel{
		conn:  conn,
		table: `"public"."file_shares"`,
	}
}

func (m *defaultFileSharesModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultFileSharesModel) FindOne(ctx context.Context, id int64) (*FileShares, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", fileSharesRows, m.table)
	var resp FileShares
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFileSharesModel) FindOneByShareId(ctx context.Context, shareId string) (*FileShares, error) {
	var resp FileShares
	query := fmt.Sprintf("select %s from %s where share_id = $1 limit 1", fileSharesRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, shareId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFileSharesModel) Insert(ctx context.Context, data *FileShares) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6)", m.table, fileSharesRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.ShareId, data.FileId, data.UserId, data.ExpireTime, data.AccessCount, data.Status)
	return ret, err
}

func (m *defaultFileSharesModel) Update(ctx context.Context, newData *FileShares) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, fileSharesRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.ShareId, newData.FileId, newData.UserId, newData.ExpireTime, newData.AccessCount, newData.Status)
	return err
}

func (m *defaultFileSharesModel) tableName() string {
	return m.table
}
