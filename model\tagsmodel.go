package model

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ TagsModel = (*customTagsModel)(nil)

type (
	// TagsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTagsModel.
	TagsModel interface {
		tagsModel
		withSession(session sqlx.Session) TagsModel
		FindOneByTagName(ctx context.Context, tagName string) (*Tags, error)
	}

	customTagsModel struct {
		*defaultTagsModel
	}
)

// NewTagsModel returns a model for the database table.
func NewTagsModel(conn sqlx.SqlConn) TagsModel {
	return &customTagsModel{
		defaultTagsModel: newTagsModel(conn),
	}
}

func (m *customTagsModel) FindOneByTagName(ctx context.Context, tagName string) (*Tags, error) {
	query := fmt.Sprintf("select %s from %s where `tag_name` = ? limit 1", tagsRows, m.table)
	var resp Tags
	err := m.conn.QueryRowCtx(ctx, &resp, query, tagName)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

func (m *customTagsModel) withSession(session sqlx.Session) TagsModel {
	return NewTagsModel(sqlx.NewSqlConnFromSession(session))
}
