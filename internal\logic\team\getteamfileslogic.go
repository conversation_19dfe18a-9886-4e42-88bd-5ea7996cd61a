package team

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTeamFilesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取团队文件列表
func NewGetTeamFilesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTeamFilesLogic {
	return &GetTeamFilesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTeamFilesLogic) GetTeamFiles(req *types.TeamFileListReq) (resp *types.FileListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
