// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	foldersFieldNames          = builder.RawFieldNames(&Folders{}, true)
	foldersRows                = strings.Join(foldersFieldNames, ",")
	foldersRowsExpectAutoSet   = strings.Join(stringx.Remove(foldersFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	foldersRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(foldersFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	foldersModel interface {
		Insert(ctx context.Context, data *Folders) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Folders, error)
		FindOneByFolderId(ctx context.Context, folderId string) (*Folders, error)
		Update(ctx context.Context, data *Folders) error
		Delete(ctx context.Context, id int64) error
	}

	defaultFoldersModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Folders struct {
		Id          int64          `db:"id"`
		FolderId    string         `db:"folder_id"`
		FolderName  string         `db:"folder_name"`
		ParentId    sql.NullString `db:"parent_id"`
		UserId      int64          `db:"user_id"`
		Status      int64          `db:"status"`
		CreateTime  time.Time      `db:"create_time"`
		UpdateTime  time.Time      `db:"update_time"`
		DeleteTime  sql.NullTime   `db:"deleteTime"`
		Description sql.NullString `db:"description"`
	}
)

func newFoldersModel(conn sqlx.SqlConn) *defaultFoldersModel {
	return &defaultFoldersModel{
		conn:  conn,
		table: `"public"."folders"`,
	}
}

func (m *defaultFoldersModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultFoldersModel) FindOne(ctx context.Context, id int64) (*Folders, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", foldersRows, m.table)
	var resp Folders
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFoldersModel) FindOneByFolderId(ctx context.Context, folderId string) (*Folders, error) {
	var resp Folders
	query := fmt.Sprintf("select %s from %s where folder_id = $1 limit 1", foldersRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, folderId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFoldersModel) Insert(ctx context.Context, data *Folders) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6, $7)", m.table, foldersRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.FolderId, data.FolderName, data.ParentId, data.UserId, data.Status, data.DeleteTime, data.Description)
	return ret, err
}

func (m *defaultFoldersModel) Update(ctx context.Context, newData *Folders) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, foldersRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.FolderId, newData.FolderName, newData.ParentId, newData.UserId, newData.Status, newData.DeleteTime, newData.Description)
	return err
}

func (m *defaultFoldersModel) tableName() string {
	return m.table
}
