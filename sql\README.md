# 论文编辑器API服务数据库设计

本文档描述了论文编辑器API服务的数据库设计，包括表结构、关系和索引。

## 数据库表结构

### 用户与会员相关表

#### membership_types - 会员类型表
- `id`: 自增主键
- `name`: 会员类型名称
- `description`: 会员类型描述
- `priority`: 优先级（数字越大权限越高）
- `create_time`: 创建时间
- `update_time`: 更新时间

#### users - 用户表
- `id`: 自增主键
- `user_id`: 用户唯一标识符
- `username`: 用户名
- `password`: 密码（加密存储）
- `nickname`: 昵称
- `avatar`: 头像URL
- `email`: 电子邮件
- `phone`: 手机号码
- `membership_type_id`: 会员类型ID
- `membership_expire_time`: 会员过期时间
- `storage_quota`: 存储配额（字节）
- `knowledge_quota`: 知识库条目配额
- `parent_user_id`: 父用户ID（用于机构会员的子账号）
- `status`: 状态（1: 正常, 0: 禁用）
- `create_time`: 创建时间
- `update_time`: 更新时间

#### orders - 订单表
- `id`: 自增主键
- `order_id`: 订单唯一标识符
- `user_id`: 用户ID
- `membership_type_id`: 会员类型ID
- `amount`: 订单金额
- `duration`: 购买时长（月）
- `status`: 状态（0: 未支付, 1: 已支付, 2: 已取消）
- `payment_method`: 支付方式
- `payment_time`: 支付时间
- `create_time`: 创建时间
- `update_time`: 更新时间

#### payment_records - 支付记录表
- `id`: 自增主键
- `order_id`: 订单ID
- `transaction_id`: 第三方支付交易ID
- `amount`: 支付金额
- `status`: 状态（0: 处理中, 1: 成功, 2: 失败）
- `payment_method`: 支付方式
- `payment_time`: 支付时间

### 网盘相关表

#### folders - 文件夹表
- `id`: 自增主键
- `folder_id`: 文件夹唯一标识符
- `folder_name`: 文件夹名称
- `parent_id`: 父文件夹ID
- `user_id`: 所属用户ID
- `status`: 状态（1: 正常, 0: 删除）
- `create_time`: 创建时间
- `update_time`: 更新时间

#### files - 文件表
- `id`: 自增主键
- `file_id`: 文件唯一标识符
- `file_name`: 文件名
- `file_size`: 文件大小（字节）
- `file_type`: 文件类型
- `folder_id`: 所属文件夹ID
- `user_id`: 所属用户ID
- `path`: 文件存储路径
- `status`: 状态（1: 正常, 0: 删除）
- `create_time`: 创建时间
- `update_time`: 更新时间

#### file_versions - 文件版本表
- `id`: 自增主键
- `file_id`: 文件ID
- `version`: 版本号
- `file_size`: 文件大小
- `path`: 文件存储路径
- `remark`: 版本备注
- `user_id`: 创建用户ID
- `create_time`: 创建时间

#### file_shares - 文件分享表
- `id`: 自增主键
- `share_id`: 分享唯一标识符
- `file_id`: 文件ID
- `user_id`: 分享用户ID
- `expire_time`: 过期时间
- `access_count`: 访问次数
- `status`: 状态（1: 有效, 0: 无效）
- `create_time`: 创建时间

### 知识库相关表

#### categories - 知识分类表
- `id`: 自增主键
- `name`: 分类名称
- `user_id`: 所属用户ID
- `parent_id`: 父分类ID
- `status`: 状态（1: 正常, 0: 删除）
- `create_time`: 创建时间
- `update_time`: 更新时间

#### tags - 标签表
- `id`: 自增主键
- `name`: 标签名称
- `user_id`: 所属用户ID
- `create_time`: 创建时间

#### knowledge - 知识点表
- `id`: 自增主键
- `knowledge_id`: 知识点唯一标识符
- `title`: 标题
- `content`: 内容
- `category_id`: 分类ID
- `user_id`: 所属用户ID
- `status`: 状态（1: 正常, 0: 删除）
- `create_time`: 创建时间
- `update_time`: 更新时间
- `search_vector`: 全文搜索向量（PostgreSQL特性）

#### knowledge_vectors - 知识点向量表（用于RAG）
- `id`: 自增主键
- `knowledge_id`: 知识点ID
- `chunk_index`: 分块索引
- `chunk_content`: 分块内容
- `vector_data`: 向量数据（二进制格式）
- `create_time`: 创建时间
- `update_time`: 更新时间

#### knowledge_tag - 知识点标签关联表
- `id`: 自增主键
- `knowledge_id`: 知识点ID
- `tag_id`: 标签ID

#### knowledge_references - 知识点引用表
- `id`: 自增主键
- `source_id`: 源知识点ID
- `target_id`: 目标知识点ID
- `create_time`: 创建时间

### 权限相关表

#### casbin_rule - Casbin规则表
- `id`: 自增主键
- `ptype`: 策略类型
- `v0` ~ `v5`: 规则参数

#### operation_logs - 操作日志表
- `id`: 自增主键
- `user_id`: 用户ID
- `action`: 操作类型
- `resource_type`: 资源类型
- `resource_id`: 资源ID
- `details`: 详细信息
- `ip_address`: IP地址
- `user_agent`: 用户代理
- `create_time`: 创建时间

## 会员体系设计

系统包含以下会员类型，按权限从低到高排序：

1. **普通用户**：基础功能访问权限
2. **月度会员**：每月付费会员，拥有更多功能和资源
3. **季度会员**：每季度付费会员，拥有更多功能和资源
4. **年度会员**：每年付费会员，拥有更多功能和资源
5. **超级会员**：永久会员，拥有全部功能和最高资源配额
6. **机构会员**：面向机构的会员类型，可包含多个子账号

## 权限控制设计

使用Casbin进行基于角色的访问控制(RBAC)：

- 角色继承关系：高级会员继承低级会员的所有权限
- 权限定义格式：`ptype, sub, obj, act`
  - `ptype`: 策略类型（p: 策略, g: 角色继承关系）
  - `sub`: 主体（用户或角色）
  - `obj`: 资源
  - `act`: 操作

## RAG（检索增强生成）支持

为支持RAG，系统设计了知识点向量表：

1. 将知识点内容分块存储
2. 为每个分块生成向量表示
3. 支持向量相似度搜索

注意：虽然PostgreSQL可以通过pgvector扩展支持向量搜索，但对于生产环境，建议考虑专门的向量数据库或搜索引擎（如Milvus、Elasticsearch、Pinecone等）以获得更好的性能和扩展性。

## 数据库初始化

数据库初始化包括：

1. 创建会员类型
2. 创建管理员用户
3. 创建默认知识分类
4. 初始化Casbin权限规则

## 如何使用

1. 确保已安装PostgreSQL数据库
2. 创建数据库：`CREATE DATABASE paper_editor;`
3. 连接到数据库：`\c paper_editor`
4. 执行schema.sql脚本：`\i schema.sql`

## 注意事项

1. 对于RAG功能，建议考虑使用专门的向量数据库或搜索引擎
2. 可以安装PostgreSQL的pgvector扩展作为向量搜索的替代方案
3. 生产环境中应确保敏感数据的安全性，如用户密码已加密存储