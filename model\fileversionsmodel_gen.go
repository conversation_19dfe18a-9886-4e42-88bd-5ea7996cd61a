// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	fileVersionsFieldNames          = builder.RawFieldNames(&FileVersions{}, true)
	fileVersionsRows                = strings.Join(fileVersionsFieldNames, ",")
	fileVersionsRowsExpectAutoSet   = strings.Join(stringx.Remove(fileVersionsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	fileVersionsRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(fileVersionsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	fileVersionsModel interface {
		Insert(ctx context.Context, data *FileVersions) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*FileVersions, error)
		FindOneByFileIdVersion(ctx context.Context, fileId string, version int64) (*FileVersions, error)
		Update(ctx context.Context, data *FileVersions) error
		Delete(ctx context.Context, id int64) error
	}

	defaultFileVersionsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	FileVersions struct {
		Id         int64          `db:"id"`
		FileId     string         `db:"file_id"`
		Version    int64          `db:"version"`
		FileSize   int64          `db:"file_size"`
		Path       string         `db:"path"`
		Remark     sql.NullString `db:"remark"`
		UserId     int64          `db:"user_id"`
		CreateTime time.Time      `db:"create_time"`
	}
)

func newFileVersionsModel(conn sqlx.SqlConn) *defaultFileVersionsModel {
	return &defaultFileVersionsModel{
		conn:  conn,
		table: `"public"."file_versions"`,
	}
}

func (m *defaultFileVersionsModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultFileVersionsModel) FindOne(ctx context.Context, id int64) (*FileVersions, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", fileVersionsRows, m.table)
	var resp FileVersions
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFileVersionsModel) FindOneByFileIdVersion(ctx context.Context, fileId string, version int64) (*FileVersions, error) {
	var resp FileVersions
	query := fmt.Sprintf("select %s from %s where file_id = $1 and version = $2 limit 1", fileVersionsRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, fileId, version)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFileVersionsModel) Insert(ctx context.Context, data *FileVersions) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6)", m.table, fileVersionsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.FileId, data.Version, data.FileSize, data.Path, data.Remark, data.UserId)
	return ret, err
}

func (m *defaultFileVersionsModel) Update(ctx context.Context, newData *FileVersions) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, fileVersionsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.FileId, newData.Version, newData.FileSize, newData.Path, newData.Remark, newData.UserId)
	return err
}

func (m *defaultFileVersionsModel) tableName() string {
	return m.table
}
