// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	teamsFieldNames          = builder.RawFieldNames(&Teams{}, true)
	teamsRows                = strings.Join(teamsFieldNames, ",")
	teamsRowsExpectAutoSet   = strings.Join(stringx.Remove(teamsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	teamsRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(teamsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	teamsModel interface {
		Insert(ctx context.Context, data *Teams) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Teams, error)
		FindOneByTeamId(ctx context.Context, teamId string) (*Teams, error)
		Update(ctx context.Context, data *Teams) error
		Delete(ctx context.Context, id int64) error
	}

	defaultTeamsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Teams struct {
		Id          int64          `db:"id"`
		TeamId      string         `db:"team_id"`
		TeamName    string         `db:"team_name"`
		Description sql.NullString `db:"description"`
		OwnerId     int64          `db:"owner_id"`
		Status      int64          `db:"status"`
		CreateTime  time.Time      `db:"create_time"`
		UpdateTime  time.Time      `db:"update_time"`
	}
)

func newTeamsModel(conn sqlx.SqlConn) *defaultTeamsModel {
	return &defaultTeamsModel{
		conn:  conn,
		table: `"public"."teams"`,
	}
}

func (m *defaultTeamsModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultTeamsModel) FindOne(ctx context.Context, id int64) (*Teams, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", teamsRows, m.table)
	var resp Teams
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTeamsModel) FindOneByTeamId(ctx context.Context, teamId string) (*Teams, error) {
	var resp Teams
	query := fmt.Sprintf("select %s from %s where team_id = $1 limit 1", teamsRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, teamId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTeamsModel) Insert(ctx context.Context, data *Teams) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5)", m.table, teamsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.TeamId, data.TeamName, data.Description, data.OwnerId, data.Status)
	return ret, err
}

func (m *defaultTeamsModel) Update(ctx context.Context, newData *Teams) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, teamsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.TeamId, newData.TeamName, newData.Description, newData.OwnerId, newData.Status)
	return err
}

func (m *defaultTeamsModel) tableName() string {
	return m.table
}
