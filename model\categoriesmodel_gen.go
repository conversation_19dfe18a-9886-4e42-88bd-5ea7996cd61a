// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	categoriesFieldNames          = builder.RawFieldNames(&Categories{}, true)
	categoriesRows                = strings.Join(categoriesFieldNames, ",")
	categoriesRowsExpectAutoSet   = strings.Join(stringx.Remove(categoriesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	categoriesRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(categoriesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	categoriesModel interface {
		Insert(ctx context.Context, data *Categories) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Categories, error)
		Update(ctx context.Context, data *Categories) error
		Delete(ctx context.Context, id int64) error
	}

	defaultCategoriesModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Categories struct {
		Id         int64         `db:"id"`
		Name       string        `db:"name"`
		UserId     int64         `db:"user_id"`
		ParentId   sql.NullInt64 `db:"parent_id"`
		Status     int64         `db:"status"`
		CreateTime time.Time     `db:"create_time"`
		UpdateTime time.Time     `db:"update_time"`
	}
)

func newCategoriesModel(conn sqlx.SqlConn) *defaultCategoriesModel {
	return &defaultCategoriesModel{
		conn:  conn,
		table: `"public"."categories"`,
	}
}

func (m *defaultCategoriesModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultCategoriesModel) FindOne(ctx context.Context, id int64) (*Categories, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", categoriesRows, m.table)
	var resp Categories
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultCategoriesModel) Insert(ctx context.Context, data *Categories) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4)", m.table, categoriesRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Name, data.UserId, data.ParentId, data.Status)
	return ret, err
}

func (m *defaultCategoriesModel) Update(ctx context.Context, data *Categories) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, categoriesRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Id, data.Name, data.UserId, data.ParentId, data.Status)
	return err
}

func (m *defaultCategoriesModel) tableName() string {
	return m.table
}
