package team

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTeamMembersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取团队成员列表
func NewGetTeamMembersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTeamMembersLogic {
	return &GetTeamMembersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTeamMembersLogic) GetTeamMembers() (resp []types.TeamMemberInfo, err error) {
	// todo: add your logic here and delete this line

	return
}
