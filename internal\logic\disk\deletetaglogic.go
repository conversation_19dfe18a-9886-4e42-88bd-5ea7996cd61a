package disk

import (
	"context"
	"errors"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
	"paper-editor-api/internal/svc"
)

type DeleteTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除标签
func NewDeleteTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteTagLogic {
	return &DeleteTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// DeleteTag 删除标签
// 参数:
//
//	无显式参数
//
// 返回值:
//
//	resp bool - 删除操作是否成功
//	err error - 错误信息，如果操作成功则为nil
func (l *DeleteTagLogic) DeleteTag() (resp bool, err error) {
	// 从上下文中获取tagId
	tagId, ok := l.ctx.Value("tagId").(string)
	if !ok {
		return false, errors.New("获取用户信息失败")
	}
	// 将tagId转换为int64类型
	covertagId, err := strconv.ParseInt(tagId, 10, 64)
	// 调用模型层删除标签
	err = l.svcCtx.FileTagsModel.Delete(l.ctx, covertagId)
	if err != nil {
		return false, errors.New("删除文件失败")
	}

	return true, nil
}
