-- 文件标签表
CREATE TABLE IF NOT EXISTS file_tags (
    id SERIAL PRIMARY KEY,
    tag_name VARCHAR(50) NOT NULL COMMENT '标签名称',
    color VARCHAR(7) DEFAULT '#1890ff' COMMENT '标签颜色',
    user_id BIGINT NOT NULL COMMENT '创建者用户ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_tag_user (tag_name, user_id)
) COMMENT='文件标签表';

-- 文件标签关联表
CREATE TABLE IF NOT EXISTS file_tag_relations (
    id SERIAL PRIMARY KEY,
    file_id VARCHAR(50) NOT NULL COMMENT '文件ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_file_tag (file_id, tag_id),
    INDEX idx_file_id (file_id),
    INDEX idx_tag_id (tag_id),
    INDEX idx_user_id (user_id)
) COMMENT='文件标签关联表';

-- 文件标星表
CREATE TABLE IF NOT EXISTS starred_files (
    id SERIAL PRIMARY KEY,
    file_id VARCHAR(50) NOT NULL COMMENT '文件ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '标星时间',
    UNIQUE KEY uk_file_user (file_id, user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time)
) COMMENT='文件标星表';

-- 文件访问日志表
CREATE TABLE IF NOT EXISTS file_access_logs (
    id SERIAL PRIMARY KEY,
    file_id VARCHAR(50) NOT NULL COMMENT '文件ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    access_type TINYINT DEFAULT 1 COMMENT '访问类型：1-查看 2-下载 3-编辑',
    access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
    ip_address VARCHAR(45) COMMENT '访问IP地址',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_file_user (file_id, user_id),
    INDEX idx_user_time (user_id, access_time),
    INDEX idx_access_time (access_time)
) COMMENT='文件访问日志表';

-- 团队表
CREATE TABLE IF NOT EXISTS teams (
    id SERIAL PRIMARY KEY,
    team_id VARCHAR(50) NOT NULL UNIQUE COMMENT '团队唯一标识',
    team_name VARCHAR(100) NOT NULL COMMENT '团队名称',
    description TEXT COMMENT '团队描述',
    owner_id BIGINT NOT NULL COMMENT '团队所有者ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常 2-禁用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_owner_id (owner_id),
    INDEX idx_team_name (team_name)
) COMMENT='团队表';

-- 团队成员表
CREATE TABLE IF NOT EXISTS team_members (
    id SERIAL PRIMARY KEY,
    team_id VARCHAR(50) NOT NULL COMMENT '团队ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) DEFAULT 'member' COMMENT '角色：owner-所有者 admin-管理员 member-成员',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常 2-禁用',
    join_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    UNIQUE KEY uk_team_user (team_id, user_id),
    INDEX idx_team_id (team_id),
    INDEX idx_user_id (user_id)
) COMMENT='团队成员表';

-- 团队文件表（扩展files表的团队功能）
CREATE TABLE IF NOT EXISTS team_files (
    id SERIAL PRIMARY KEY,
    file_id VARCHAR(50) NOT NULL COMMENT '文件ID',
    team_id VARCHAR(50) NOT NULL COMMENT '团队ID',
    permission TINYINT DEFAULT 1 COMMENT '权限：1-只读 2-读写 3-管理',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_file_team (file_id, team_id),
    INDEX idx_team_id (team_id),
    INDEX idx_file_id (file_id)
) COMMENT='团队文件表';

-- 为现有的files表添加is_starred字段（如果不存在）
ALTER TABLE files ADD COLUMN IF NOT EXISTS is_starred BOOLEAN DEFAULT FALSE COMMENT '是否标星';

-- 为现有的file_shares表添加password字段（如果不存在）
ALTER TABLE file_shares ADD COLUMN IF NOT EXISTS password VARCHAR(50) COMMENT '分享密码';

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_files_user_status ON files(user_id, status);
CREATE INDEX IF NOT EXISTS idx_files_folder_status ON files(folder_id, status);
CREATE INDEX IF NOT EXISTS idx_files_starred ON files(user_id, is_starred) WHERE is_starred = TRUE;
CREATE INDEX IF NOT EXISTS idx_file_shares_status ON file_shares(status, expire_time);