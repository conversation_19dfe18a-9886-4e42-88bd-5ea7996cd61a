package user

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/user"
	"paper-editor-api/internal/svc"
)

// 上传头像
func UploadAvatarHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 解析表单
		err := r.ParseMultipartForm(10 << 20) // 10MB
		if err != nil {
			httpx.Error(w, err)
			return
		}

		// 获取文件
		file, header, err := r.FormFile("avatar")
		if err != nil {
			httpx.Error(w, err)
			return
		}
		defer file.Close()

		l := user.NewUploadAvatarLogic(r.Context(), svcCtx)
		resp, err := l.UploadAvatar(header)
		response.Response(w, resp, err)
	}
}
