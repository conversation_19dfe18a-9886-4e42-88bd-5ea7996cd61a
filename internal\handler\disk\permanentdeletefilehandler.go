package disk

import (
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/disk"
	"paper-editor-api/internal/svc"
)

// 彻底删除文件
func PermanentDeleteFileHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := disk.NewPermanentDeleteFileLogic(r.Context(), svcCtx)
		resp, err := l.PermanentDeleteFile()
		response.Response(w, resp, err)
	}
}
