# 论文编辑器API服务 (Go-Zero版)

这是一个基于Go-Zero框架的论文编辑器API服务，提供网盘功能和知识库管理功能，用于支持论文编辑器的后端服务。

## 项目结构

```
paper-editor-api/
├── api/                # API定义文件
│   └── editor.api      # 编辑器服务API定义
├── etc/                # 配置文件目录
│   └── editor-api.yaml # 服务配置文件
├── internal/           # 内部代码
│   ├── config/         # 配置结构定义
│   ├── handler/        # HTTP处理器
│   ├── logic/          # 业务逻辑
│   ├── svc/            # 服务上下文
│   └── types/          # 类型定义
├── model/              # 数据模型
│   ├── disk/           # 网盘相关模型
│   └── knowledge/      # 知识库相关模型
├── editor.go           # 主程序入口
├── go.mod              # Go模块定义
└── go.sum              # 依赖版本锁定
```

## 功能特性

- **网盘功能**
  - 文件上传与下载
  - 文件管理（创建、删除、重命名、移动）
  - 文件分享
  - 文件版本控制

- **知识库功能**
  - 知识点管理
  - 资料收集与整理
  - 标签与分类
  - 全文检索

- **用户管理**
  - 用户认证与授权
  - 权限控制

## 开发环境准备

1. 安装Go 1.20或更高版本
2. 安装Go-Zero工具链

```bash
go install github.com/zeromicro/go-zero/tools/goctl@latest
```

## 如何运行

1. 安装依赖

```bash
go mod tidy
```

2. 启动服务

```bash
go run editor.go -f etc/editor-api.yaml
```

或者使用编译后的二进制文件：

```bash
go build -o editor-api editor.go
./editor-api -f etc/editor-api.yaml
```

## API文档

### 网盘API

#### 上传文件
- 路径: `/api/v1/disk/upload`
- 方法: `POST`
- 请求: `multipart/form-data`
- 响应:
  ```json
  {
    "code": 200,
    "message": "上传成功",
    "data": {
      "file_id": "file_20250527162700_abc123",
      "file_name": "论文草稿.docx",
      "file_size": 1024000,
      "file_type": "docx",
      "upload_time": "2025-05-27 16:27:00"
    }
  }
  ```

#### 下载文件
- 路径: `/api/v1/disk/download/{fileId}`
- 方法: `GET`
- 响应: 文件流

#### 文件列表
- 路径: `/api/v1/disk/files`
- 方法: `GET`
- 参数: `folder_id`, `page`, `page_size`
- 响应:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "total": 100,
      "files": [
        {
          "file_id": "file_20250527162700_abc123",
          "file_name": "论文草稿.docx",
          "file_size": 1024000,
          "file_type": "docx",
          "upload_time": "2025-05-27 16:27:00"
        }
      ]
    }
  }
  ```

### 知识库API

#### 添加知识点
- 路径: `/api/v1/knowledge/add`
- 方法: `POST`
- 请求体:
  ```json
  {
    "title": "知识点标题",
    "content": "知识点内容",
    "tags": ["标签1", "标签2"],
    "category_id": 1
  }
  ```
- 响应:
  ```json
  {
    "code": 200,
    "message": "添加成功",
    "data": {
      "knowledge_id": "k_20250527162700_abc123"
    }
  }
  ```

#### 搜索知识点
- 路径: `/api/v1/knowledge/search`
- 方法: `GET`
- 参数: `keyword`, `tags`, `category_id`, `page`, `page_size`
- 响应:
  ```json
  {
    "code": 200,
    "message": "搜索成功",
    "data": {
      "total": 50,
      "items": [
        {
          "knowledge_id": "k_20250527162700_abc123",
          "title": "知识点标题",
          "content_preview": "知识点内容预览...",
          "tags": ["标签1", "标签2"],
          "category": "分类名称",
          "create_time": "2025-05-27 16:27:00"
        }
      ]
    }
  }
  ```

## 配置说明

配置文件位于 `etc/editor-api.yaml`，包含以下主要配置项：

- 服务名称和监听地址
- 日志配置
- 数据库配置
- Redis配置
- 文件存储配置
- 搜索引擎配置

## 开发指南

### 添加新API

1. 在 `api/editor.api` 文件中定义新的API
2. 运行 goctl 生成代码
3. 实现对应的 logic 层业务逻辑

### 代码生成

使用 goctl 工具可以根据API定义生成代码：

```bash
goctl api go -api api/main.api -dir .
```

## 数据库设计

服务使用PostgreSQL作为主要数据库，包含以下主要表：

- `users` - 用户信息
- `files` - 文件信息
- `folders` - 文件夹信息
- `file_versions` - 文件版本信息
- `knowledge` - 知识点信息
- `tags` - 标签信息
- `categories` - 分类信息

## 技术栈

- Go-Zero - 微服务框架
- PostgreSQL - 关系型数据库
- Redis - 缓存和会话管理
- MinIO/OSS - 对象存储（文件存储）
- Elasticsearch - 全文检索引擎
