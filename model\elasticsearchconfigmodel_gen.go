// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	elasticsearchConfigFieldNames          = builder.RawFieldNames(&ElasticsearchConfig{}, true)
	elasticsearchConfigRows                = strings.Join(elasticsearchConfigFieldNames, ",")
	elasticsearchConfigRowsExpectAutoSet   = strings.Join(stringx.Remove(elasticsearchConfigFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	elasticsearchConfigRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(elasticsearchConfigFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	elasticsearchConfigModel interface {
		Insert(ctx context.Context, data *ElasticsearchConfig) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ElasticsearchConfig, error)
		Update(ctx context.Context, data *ElasticsearchConfig) error
		Delete(ctx context.Context, id int64) error
	}

	defaultElasticsearchConfigModel struct {
		conn  sqlx.SqlConn
		table string
	}

	ElasticsearchConfig struct {
		Id          int64          `db:"id"`
		IndexName   string         `db:"index_name"`
		Description sql.NullString `db:"description"`
		Settings    sql.NullString `db:"settings"`
		Mappings    sql.NullString `db:"mappings"`
		CreateTime  time.Time      `db:"create_time"`
		UpdateTime  time.Time      `db:"update_time"`
	}
)

func newElasticsearchConfigModel(conn sqlx.SqlConn) *defaultElasticsearchConfigModel {
	return &defaultElasticsearchConfigModel{
		conn:  conn,
		table: `"public"."elasticsearch_config"`,
	}
}

func (m *defaultElasticsearchConfigModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultElasticsearchConfigModel) FindOne(ctx context.Context, id int64) (*ElasticsearchConfig, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", elasticsearchConfigRows, m.table)
	var resp ElasticsearchConfig
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultElasticsearchConfigModel) Insert(ctx context.Context, data *ElasticsearchConfig) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4)", m.table, elasticsearchConfigRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.IndexName, data.Description, data.Settings, data.Mappings)
	return ret, err
}

func (m *defaultElasticsearchConfigModel) Update(ctx context.Context, data *ElasticsearchConfig) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, elasticsearchConfigRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Id, data.IndexName, data.Description, data.Settings, data.Mappings)
	return err
}

func (m *defaultElasticsearchConfigModel) tableName() string {
	return m.table
}
