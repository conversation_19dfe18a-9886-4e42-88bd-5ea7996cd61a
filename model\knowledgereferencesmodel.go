package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ KnowledgeReferencesModel = (*customKnowledgeReferencesModel)(nil)

type (
	// KnowledgeReferencesModel is an interface to be customized, add more methods here,
	// and implement the added methods in customKnowledgeReferencesModel.
	KnowledgeReferencesModel interface {
		knowledgeReferencesModel
		withSession(session sqlx.Session) KnowledgeReferencesModel
	}

	customKnowledgeReferencesModel struct {
		*defaultKnowledgeReferencesModel
	}
)

// NewKnowledgeReferencesModel returns a model for the database table.
func NewKnowledgeReferencesModel(conn sqlx.SqlConn) KnowledgeReferencesModel {
	return &customKnowledgeReferencesModel{
		defaultKnowledgeReferencesModel: newKnowledgeReferencesModel(conn),
	}
}

func (m *customKnowledgeReferencesModel) withSession(session sqlx.Session) KnowledgeReferencesModel {
	return NewKnowledgeReferencesModel(sqlx.NewSqlConnFromSession(session))
}
