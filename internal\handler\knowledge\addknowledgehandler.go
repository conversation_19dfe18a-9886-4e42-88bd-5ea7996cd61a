package knowledge

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/knowledge"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 添加知识点
func AddKnowledgeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AddKnowledgeReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := knowledge.NewAddKnowledgeLogic(r.Context(), svcCtx)
		resp, err := l.AddKnowledge(&req)
		response.Response(w, resp, err)
	}
}
