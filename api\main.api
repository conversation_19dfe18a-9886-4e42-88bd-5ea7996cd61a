syntax = "v1"

info (
	title:   "论文编辑器API服务"
	desc:    "提供网盘功能和知识库管理功能的API服务"
	author:  "CodeBuddy"
	email:   "<EMAIL>"
	version: "v1"
)

// 用户相关API
type (
	// 用户注册
	RegisterReq {
		Username string `json:"username"`
		Password string `json:"password"`
		Email    string `json:"email,optional"`
		Nickname string `json:"nickname,optional"`
	}
	RegisterResp {
		UserId string `json:"user_id"`
	}
	// 用户登录
	LoginReq {
		Username string `json:"username"`
		Password string `json:"password"`
	}
	LoginResp {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		Expires      int64  `json:"expires"`
	}
	// 刷新令牌
	RefreshTokenReq {
		RefreshToken string `json:"refresh_token"`
	}
	RefreshTokenResp {
		AccessToken string `json:"access_token"`
		Expires     int64  `json:"expires"`
	}
	// 用户信息
	UserInfoResp {
		UserId         string `json:"user_id"`
		Username       string `json:"username"`
		Nickname       string `json:"nickname"`
		Avatar         string `json:"avatar"`
		Email          string `json:"email"`
		Phone          string `json:"phone"`
		MembershipType string `json:"membership_type"`
		StorageQuota   int64  `json:"storage_quota"`
		KnowledgeQuota int64  `json:"knowledge_quota"`
		CreateTime     string `json:"create_time"`
	}
	// 更新用户信息
	UpdateUserInfoReq {
		Nickname string `json:"nickname,optional"`
		Email    string `json:"email,optional"`
		Phone    string `json:"phone,optional"`
	}
	// 修改密码
	ChangePasswordReq {
		OldPassword string `json:"old_password"`
		NewPassword string `json:"new_password"`
	}
	// 上传头像
	UploadAvatarResp {
		AvatarUrl string `json:"avatar_url"`
	}
	// 重置密码
	ResetPasswordReq {
		Email string `json:"email"`
	}
	ResetPasswordResp {
		Message string `json:"message"`
	}
	// 验证重置密码
	VerifyResetPasswordReq {
		Token       string `json:"token"`
		NewPassword string `json:"new_password"`
	}
)

// 网盘相关API
type (
	// 文件信息
	FileInfo {
		FileId     string   `json:"file_id"`
		FileName   string   `json:"file_name"`
		FileSize   int64    `json:"file_size"`
		FileType   string   `json:"file_type"`
		UploadTime string   `json:"upload_time"`
		UpdateTime string   `json:"update_time"`
		FolderId   string   `json:"folder_id"`
		IsStarred  bool     `json:"is_starred"`
		Tags       []string `json:"tags"`
		Status     int      `json:"status"` // 1:正常 2:回收站 3:已删除
	}
	// 文件夹信息
	FolderInfo {
		FolderId   string `json:"folder_id"`
		FolderName string `json:"folder_name"`
		ParentId   string `json:"parent_id"`
		CreateTime string `json:"create_time"`
		UpdateTime string `json:"update_time"`
		Status     string `json:"status"`
	}
	// 文件列表请求
	FileListReq {
		FolderId string `form:"folder_id,optional"`
		TagName  string `form:"tag_name,optional"`
		Page     int    `form:"page,default=1"`
		PageSize int    `form:"page_size,default=20"`
	}
	// 文件列表响应
	FileListResp {
		Total       int64        `json:"total"`
		Files       []FileInfo   `json:"files"`
		Folders     []FolderInfo `json:"folders"`
		FileCount   int64        `json:"file_count"`
		FolderCount int64        `json:"folder_count"`
	}
	// 创建文件夹请求
	CreateFolderReq {
		FolderName string `json:"folder_name"`
		ParentId   string `json:"parent_id,optional"`
	}
	// 创建文件夹响应
	CreateFolderResp {
		FolderId string `json:"folder_id"`
	}
	// 重命名文件请求
	RenameFileReq {
		FileId   string `json:"file_id"`
		FileName string `json:"file_name"`
	}
	// 移动文件请求
	MoveFileReq {
		FileId   string `json:"file_id"`
		FolderId string `json:"folder_id"`
	}
	// 文件详情响应
	FileDetailResp {
		FileInfo FileInfo `json:"file_info"`
		Url      string   `json:"url"`
		Expires  int64    `json:"expires"`
	}
	// 文件下载响应
	FileDownloadResp {
		Url string `json:"url"`
	}
	// 标签信息
	TagInfo {
		TagId     int64  `json:"tag_id"`
		TagName   string `json:"tag_name"`
		Color     string `json:"color"`
		FileCount int64  `json:"file_count"`
	}
	// 创建标签请求
	CreateTagReq {
		TagName string `json:"tag_name"`
		Color   string `json:"color,optional"`
	}
	// 创建标签响应
	CreateTagResp {
		TagId int64 `json:"tag_id"`
	}
	// 标签列表响应
	TagListResp {
		Tags []TagInfo `json:"tags"`
	}
	// 添加文件标签请求
	AddFileTagReq {
		FileId string `json:"file_id"`
		TagId  int64  `json:"tag_id"`
	}
	// 分享文件请求
	ShareFileReq {
		FileId     string `json:"file_id"`
		ExpireDays int    `json:"expire_days,optional"` // 过期天数，0表示永不过期
		Password   string `json:"password,optional"` // 分享密码
	}
	// 分享文件响应
	ShareFileResp {
		ShareId    string `json:"share_id"`
		ShareUrl   string `json:"share_url"`
		Password   string `json:"password,optional"`
		ExpireTime string `json:"expire_time,optional"`
	}
	// 团队信息
	TeamInfo {
		TeamId      string `json:"team_id"`
		TeamName    string `json:"team_name"`
		Description string `json:"description"`
		OwnerId     int64  `json:"owner_id"`
		MemberCount int    `json:"member_count"`
		CreateTime  string `json:"create_time"`
	}
	// 创建团队请求
	CreateTeamReq {
		TeamName    string `json:"team_name"`
		Description string `json:"description,optional"`
	}
	// 创建团队响应
	CreateTeamResp {
		TeamId string `json:"team_id"`
	}
	// 团队成员信息
	TeamMemberInfo {
		UserId   int64  `json:"user_id"`
		Username string `json:"username"`
		Nickname string `json:"nickname"`
		Role     string `json:"role"` // owner, admin, member
		JoinTime string `json:"join_time"`
	}
	// 邀请团队成员请求
	InviteTeamMemberReq {
		TeamId   string `json:"team_id"`
		Username string `json:"username"`
		Role     string `json:"role,default=member"`
	}
	// 团队文件列表请求
	TeamFileListReq {
		TeamId   string `form:"team_id"`
		FolderId string `form:"folder_id,optional"`
		Page     int    `form:"page,default=1"`
		PageSize int    `form:"page_size,default=20"`
	}
)

// 知识库相关API
type (
	KnowledgeInfo {
		KnowledgeId    string   `json:"knowledge_id"`
		Title          string   `json:"title"`
		ContentPreview string   `json:"content_preview"`
		Tags           []string `json:"tags"`
		Category       string   `json:"category"`
		CreateTime     string   `json:"create_time"`
		UpdateTime     string   `json:"update_time"`
	}
	AddKnowledgeReq {
		Title      string   `json:"title"`
		Content    string   `json:"content"`
		Tags       []string `json:"tags,optional"`
		CategoryId int64    `json:"category_id,optional"`
	}
	AddKnowledgeResp {
		KnowledgeId string `json:"knowledge_id"`
	}
	SearchKnowledgeReq {
		Keyword    string   `form:"keyword,optional"`
		Tags       []string `form:"tags,optional"`
		CategoryId int64    `form:"category_id,optional"`
		Page       int      `form:"page,default=1"`
		PageSize   int      `form:"page_size,default=20"`
	}
	SearchKnowledgeResp {
		Total int64           `json:"total"`
		Items []KnowledgeInfo `json:"items"`
	}
	KnowledgeDetailResp {
		KnowledgeId string   `json:"knowledge_id"`
		Title       string   `json:"title"`
		Content     string   `json:"content"`
		Tags        []string `json:"tags"`
		CategoryId  int64    `json:"category_id"`
		Category    string   `json:"category"`
		CreateTime  string   `json:"create_time"`
		UpdateTime  string   `json:"update_time"`
	}
)

// 通用响应
type (
	BaseResp {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}
)

// 用户服务
@server (
	prefix: /api/v1/user
	group:  user
)
service editor-api {
	@doc "用户注册"
	@handler register
	post /register (RegisterReq) returns (RegisterResp)

	@doc "用户登录"
	@handler login
	post /login (LoginReq) returns (LoginResp)

	@doc "刷新令牌"
	@handler refreshToken
	post /refresh (RefreshTokenReq) returns (RefreshTokenResp)

	@doc "重置密码请求"
	@handler resetPassword
	post /reset-password (ResetPasswordReq) returns (ResetPasswordResp)

	@doc "验证重置密码"
	@handler verifyResetPassword
	post /verify-reset-password (VerifyResetPasswordReq) returns (BaseResp)
}

// 需要认证的用户服务
@server (
	prefix:     /api/v1/user
	middleware: Auth
	group:      user
)
service editor-api {
	@doc "获取用户信息"
	@handler getUserInfo
	get /info returns (UserInfoResp)

	@doc "更新用户信息"
	@handler updateUserInfo
	put /info (UpdateUserInfoReq) returns (BaseResp)

	@doc "修改密码"
	@handler changePassword
	put /password (ChangePasswordReq) returns (BaseResp)

	@doc "上传头像"
	@handler uploadAvatar
	post /avatar returns (UploadAvatarResp)

	@doc "用户退出登录"
	@handler logout
	post /logout returns (BaseResp)

	@doc "删除账户"
	@handler deleteAccount
	delete /account returns (BaseResp)
}

// 网盘服务
@server (
	prefix:     /api/v1/disk
	middleware: Auth
	group:      disk
)
service editor-api {
	@doc "获取文件列表"
	@handler getFileList
	get /files (FileListReq) returns (FileListResp)

	@doc "上传文件"
	@handler uploadFile
	post /upload returns (FileInfo)

	@doc "下载文件"
	@handler downloadFile
	get /download/:fileId returns (FileDownloadResp)

	@doc "创建文件夹"
	@handler createFolder
	post /folder (CreateFolderReq) returns (CreateFolderResp)

	@doc "重命名文件"
	@handler renameFile
	put /rename (RenameFileReq) returns (BaseResp)

	@doc "移动文件"
	@handler moveFile
	put /move (MoveFileReq) returns (BaseResp)

	@doc "删除文件"
	@handler deleteFile
	delete /file/:fileId returns (BaseResp)

	@doc "获取文件详情"
	@handler getFileDetail
	get /file/:fileId returns (FileDetailResp)

	// 文件标星功能
	@doc "标星文件"
	@handler starFile
	put /star/:fileId returns (BaseResp)

	@doc "取消标星文件"
	@handler unstarFile
	delete /star/:fileId returns (BaseResp)

	@doc "获取已标星文件列表"
	@handler getStarredFiles
	get /starred (FileListReq) returns (FileListResp)

	// 最近访问功能
	@doc "获取最近访问文件列表"
	@handler getRecentFiles
	get /recent (FileListReq) returns (FileListResp)

	// 共享文件功能
	@doc "获取共享文件列表"
	@handler getSharedFiles
	get /shared (FileListReq) returns (FileListResp)

	@doc "分享文件"
	@handler shareFile
	post /share (ShareFileReq) returns (ShareFileResp)

	// 回收站功能
	@doc "获取回收站文件列表"
	@handler getTrashFiles
	get /trash (FileListReq) returns (FileListResp)

	@doc "恢复回收站文件"
	@handler restoreFile
	put /trash/restore/:fileId returns (BaseResp)

	@doc "清空回收站"
	@handler emptyTrash
	delete /trash/empty returns (BaseResp)

	@doc "彻底删除文件"
	@handler permanentDeleteFile
	delete /trash/:fileId returns (BaseResp)

	// 标签管理功能
	@doc "获取所有标签"
	@handler getAllTags
	get /tags returns (TagListResp)

	@doc "创建标签"
	@handler createTag
	post /tags (CreateTagReq) returns (CreateTagResp)

	@doc "删除标签"
	@handler deleteTag
	delete /tags/:tagId returns (BaseResp)

	@doc "为文件添加标签"
	@handler addFileTag
	post /file/tag (AddFileTagReq) returns (BaseResp)

	@doc "移除文件标签"
	@handler removeFileTag
	delete /file/:fileId/tag/:tagId returns (BaseResp)

	@doc "根据标签获取文件列表"
	@handler getFilesByTag
	get /tag/:tagId/files (FileListReq) returns (FileListResp)
}

// 团队空间服务
@server (
	prefix:     /api/v1/team
	middleware: Auth
	group:      team
)
service editor-api {
	@doc "创建团队"
	@handler createTeam
	post /create (CreateTeamReq) returns (CreateTeamResp)

	@doc "获取我的团队列表"
	@handler getMyTeams
	get /list returns ([]TeamInfo)

	@doc "获取团队详情"
	@handler getTeamDetail
	get /:teamId returns (TeamInfo)

	@doc "邀请团队成员"
	@handler inviteTeamMember
	post /invite (InviteTeamMemberReq) returns (BaseResp)

	@doc "获取团队成员列表"
	@handler getTeamMembers
	get /:teamId/members returns ([]TeamMemberInfo)

	@doc "移除团队成员"
	@handler removeTeamMember
	delete /:teamId/member/:userId returns (BaseResp)

	@doc "退出团队"
	@handler leaveTeam
	delete /:teamId/leave returns (BaseResp)

	@doc "获取团队文件列表"
	@handler getTeamFiles
	get /:teamId/files (TeamFileListReq) returns (FileListResp)

	@doc "上传团队文件"
	@handler uploadTeamFile
	post /:teamId/upload returns (FileInfo)
}

// 知识库服务
@server (
	prefix:     /api/v1/knowledge
	middleware: Auth
	group:      knowledge
)
service editor-api {
	@doc "添加知识点"
	@handler addKnowledge
	post /add (AddKnowledgeReq) returns (AddKnowledgeResp)

	@doc "搜索知识点"
	@handler searchKnowledge
	get /search (SearchKnowledgeReq) returns (SearchKnowledgeResp)

	@doc "获取知识点详情"
	@handler getKnowledgeDetail
	get /detail/:knowledgeId returns (KnowledgeDetailResp)

	@doc "删除知识点"
	@handler deleteKnowledge
	delete /:knowledgeId returns (BaseResp)

	@doc "更新知识点"
	@handler updateKnowledge
	put /:knowledgeId (AddKnowledgeReq) returns (BaseResp)
}

