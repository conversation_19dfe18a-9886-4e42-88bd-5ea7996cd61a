package user

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type LogoutLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户退出登录
func NewLogoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LogoutLogic {
	return &LogoutLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LogoutLogic) Logout() (resp *types.BaseResp, err error) {
	// todo: add your logic here and delete this line
	// 在实际应用中，这里可以：
	// 1. 将token加入黑名单
	// 2. 清除服务端session
	// 3. 记录退出日志
	//
	// 由于JWT是无状态的，客户端删除token即可实现退出
	// 这里主要用于记录用户退出行为

	l.Logger.Info("用户退出登录")
	return
}
