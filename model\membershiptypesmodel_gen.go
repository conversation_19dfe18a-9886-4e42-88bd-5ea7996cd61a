// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	membershipTypesFieldNames          = builder.RawFieldNames(&MembershipTypes{}, true)
	membershipTypesRows                = strings.Join(membershipTypesFieldNames, ",")
	membershipTypesRowsExpectAutoSet   = strings.Join(stringx.Remove(membershipTypesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	membershipTypesRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(membershipTypesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	membershipTypesModel interface {
		Insert(ctx context.Context, data *MembershipTypes) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*MembershipTypes, error)
		FindOneByName(ctx context.Context, name string) (*MembershipTypes, error)
		Update(ctx context.Context, data *MembershipTypes) error
		Delete(ctx context.Context, id int64) error
	}

	defaultMembershipTypesModel struct {
		conn  sqlx.SqlConn
		table string
	}

	MembershipTypes struct {
		Id          int64          `db:"id"`
		Name        string         `db:"name"`
		Description sql.NullString `db:"description"`
		Priority    int64          `db:"priority"`
		CreateTime  time.Time      `db:"create_time"`
		UpdateTime  time.Time      `db:"update_time"`
	}
)

func newMembershipTypesModel(conn sqlx.SqlConn) *defaultMembershipTypesModel {
	return &defaultMembershipTypesModel{
		conn:  conn,
		table: `"public"."membership_types"`,
	}
}

func (m *defaultMembershipTypesModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultMembershipTypesModel) FindOne(ctx context.Context, id int64) (*MembershipTypes, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", membershipTypesRows, m.table)
	var resp MembershipTypes
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultMembershipTypesModel) FindOneByName(ctx context.Context, name string) (*MembershipTypes, error) {
	var resp MembershipTypes
	query := fmt.Sprintf("select %s from %s where name = $1 limit 1", membershipTypesRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, name)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultMembershipTypesModel) Insert(ctx context.Context, data *MembershipTypes) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3)", m.table, membershipTypesRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Name, data.Description, data.Priority)
	return ret, err
}

func (m *defaultMembershipTypesModel) Update(ctx context.Context, newData *MembershipTypes) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, membershipTypesRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.Name, newData.Description, newData.Priority)
	return err
}

func (m *defaultMembershipTypesModel) tableName() string {
	return m.table
}
