// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	filesFieldNames          = builder.RawFieldNames(&Files{}, true)
	filesRows                = strings.Join(filesFieldNames, ",")
	filesRowsExpectAutoSet   = strings.Join(stringx.Remove(filesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	filesRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(filesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	filesModel interface {
		Insert(ctx context.Context, data *Files) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Files, error)
		FindOneByFileId(ctx context.Context, fileId string) (*Files, error)
		Update(ctx context.Context, data *Files) error
		Delete(ctx context.Context, id int64) error
	}

	defaultFilesModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Files struct {
		Id         int64          `db:"id"`
		FileId     string         `db:"file_id"`
		FileName   string         `db:"file_name"`
		FileSize   int64          `db:"file_size"`
		FileType   string         `db:"file_type"`
		FolderId   sql.NullString `db:"folder_id"`
		UserId     int64          `db:"user_id"`
		Path       string         `db:"path"`
		Status     int64          `db:"status"`
		CreateTime time.Time      `db:"create_time"`
		UpdateTime time.Time      `db:"update_time"`
	}
)

func newFilesModel(conn sqlx.SqlConn) *defaultFilesModel {
	return &defaultFilesModel{
		conn:  conn,
		table: `"public"."files"`,
	}
}

func (m *defaultFilesModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultFilesModel) FindOne(ctx context.Context, id int64) (*Files, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", filesRows, m.table)
	var resp Files
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFilesModel) FindOneByFileId(ctx context.Context, fileId string) (*Files, error) {
	var resp Files
	query := fmt.Sprintf("select %s from %s where file_id = $1 limit 1", filesRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, fileId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFilesModel) Insert(ctx context.Context, data *Files) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6, $7, $8)", m.table, filesRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.FileId, data.FileName, data.FileSize, data.FileType, data.FolderId, data.UserId, data.Path, data.Status)
	return ret, err
}

func (m *defaultFilesModel) Update(ctx context.Context, newData *Files) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, filesRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.FileId, newData.FileName, newData.FileSize, newData.FileType, newData.FolderId, newData.UserId, newData.Path, newData.Status)
	return err
}

func (m *defaultFilesModel) tableName() string {
	return m.table
}
