package user

import (
	"context"
	"database/sql"
	"errors"
	"paper-editor-api/internal/middleware"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新用户信息
func NewUpdateUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateUserInfoLogic {
	return &UpdateUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateUserInfoLogic) UpdateUserInfo(req *types.UpdateUserInfoReq) error {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return errors.New("获取用户信息失败")
	}

	// 查询用户信息
	user, err := l.svcCtx.UsersModel.FindOne(context.Background(), userId)
	if err != nil {
		return errors.New("用户不存在")
	}

	// 更新用户信息
	if req.Nickname != "" {
		user.Nickname = sql.NullString{String: req.Nickname, Valid: true}
	}
	if req.Email != "" {
		user.Email = sql.NullString{String: req.Email, Valid: true}
	}
	if req.Phone != "" {
		user.Phone = sql.NullString{String: req.Phone, Valid: true}
	}

	// 保存更新
	err = l.svcCtx.UsersModel.Update(context.Background(), user)
	if err != nil {
		return errors.New("用户信息更新失败")
	}

	return nil
}
