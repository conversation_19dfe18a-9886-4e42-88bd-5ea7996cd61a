package team

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTeamDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取团队详情
func NewGetTeamDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTeamDetailLogic {
	return &GetTeamDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTeamDetailLogic) GetTeamDetail() (resp *types.TeamInfo, err error) {
	// todo: add your logic here and delete this line

	return
}
