# Elasticsearch配置

# 连接配置
connection:
  hosts:
    - http://localhost:9200
  username: elastic
  password: changeme
  timeout: 30s

# 索引配置
indices:
  knowledge_vectors:
    settings:
      number_of_shards: 1
      number_of_replicas: 1
      analysis:
        analyzer:
          text_analyzer:
            type: custom
            tokenizer: standard
            filter:
              - lowercase
              - asciifolding
    mappings:
      properties:
        knowledge_id:
          type: keyword
        chunk_index:
          type: integer
        chunk_content:
          type: text
          analyzer: text_analyzer
        embedding:
          type: dense_vector
          dims: 1536
          index: true
          similarity: cosine
        create_time:
          type: date
        update_time:
          type: date

# RAG配置
rag:
  chunk_size: 1000
  chunk_overlap: 200
  embedding_model: text-embedding-3-small
  similarity_top_k: 5
  similarity_threshold: 0.7