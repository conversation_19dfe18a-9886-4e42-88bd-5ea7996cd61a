package knowledge

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/knowledge"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 更新知识点
func UpdateKnowledgeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AddKnowledgeReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := knowledge.NewUpdateKnowledgeLogic(r.Context(), svcCtx)
		resp, err := l.UpdateKnowledge(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
