package storage

import (
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// MinIO存储实现
type MinioStorage struct {
	client    *minio.Client
	bucket    string
	endpoint  string
	accessKey string
	secretKey string
	useSSL    bool
}

// 创建MinIO存储
func NewMinioStorage(endpoint, accessKey, secretKey, bucket string) (*MinioStorage, error) {
	// 初始化MinIO客户端
	useSSL := false // 根据实际情况设置是否使用SSL
	client, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(accessKey, secretKey, ""),
		Secure: useSSL,
	})
	if err != nil {
		return nil, fmt.Errorf("初始化MinIO客户端失败: %w", err)
	}

	// 检查存储桶是否存在，不存在则创建
	exists, err := client.BucketExists(context.Background(), bucket)
	if err != nil {
		return nil, fmt.Errorf("检查存储桶失败: %w", err)
	}

	if !exists {
		err = client.MakeBucket(context.Background(), bucket, minio.MakeBucketOptions{})
		if err != nil {
			return nil, fmt.Errorf("创建存储桶失败: %w", err)
		}
	}

	return &MinioStorage{
		client:    client,
		bucket:    bucket,
		endpoint:  endpoint,
		accessKey: accessKey,
		secretKey: secretKey,
		useSSL:    useSSL,
	}, nil
}

// 保存文件
func (s *MinioStorage) SaveFile(file *multipart.FileHeader, path string) (string, error) {
	// 打开源文件
	src, err := file.Open()
	if err != nil {
		return "", fmt.Errorf("打开源文件失败: %w", err)
	}
	defer src.Close()

	// 获取文件大小
	size := file.Size

	// 获取文件类型
	contentType := file.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// 上传文件到MinIO
	_, err = s.client.PutObject(context.Background(), s.bucket, path, src, size, minio.PutObjectOptions{
		ContentType: contentType,
	})
	if err != nil {
		return "", fmt.Errorf("上传文件到MinIO失败: %w", err)
	}

	return path, nil
}

// 获取文件
func (s *MinioStorage) GetFile(path string) (io.ReadCloser, error) {
	// 从MinIO获取文件
	obj, err := s.client.GetObject(context.Background(), s.bucket, path, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("从MinIO获取文件失败: %w", err)
	}

	return obj, nil
}

// 删除文件
func (s *MinioStorage) DeleteFile(path string) error {
	// 从MinIO删除文件
	err := s.client.RemoveObject(context.Background(), s.bucket, path, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("从MinIO删除文件失败: %w", err)
	}

	return nil
}

// 生成访问URL
func (s *MinioStorage) GenerateURL(path string, expire int64) (string, error) {
	// 检查文件是否存在
	_, err := s.client.StatObject(context.Background(), s.bucket, path, minio.StatObjectOptions{})
	if err != nil {
		return "", errors.New("文件不存在")
	}

	// 生成预签名URL
	expiry := time.Duration(expire) * time.Second
	presignedURL, err := s.client.PresignedGetObject(context.Background(), s.bucket, path, expiry, nil)
	if err != nil {
		return "", fmt.Errorf("生成预签名URL失败: %w", err)
	}

	return presignedURL.String(), nil
}
