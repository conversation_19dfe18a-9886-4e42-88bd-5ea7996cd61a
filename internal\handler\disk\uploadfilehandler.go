package disk

import (
	"errors"
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/disk"
	"paper-editor-api/internal/svc"
)

// 上传文件
func UploadFileHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 解析表单数据
		if err := r.ParseMultipartForm(10 << 20); err != nil { // 10MB max size
			response.Response(w, nil, errors.New("文件大小不能超过10MB"))
			return
		}

		// 获取上传的文件
		file, header, err := r.FormFile("file")
		if err != nil {
			response.Response(w, nil, errors.New("获取上传文件失败: "+err.Error()))
			return
		}
		defer file.Close()

		// 获取文件夹ID（可选）
		folderId := r.FormValue("folder_id")

		// 调用业务逻辑
		l := disk.NewUploadFileLogic(r.Context(), svcCtx)
		resp, err := l.UploadFile(header, folderId) // 只传递 header 而不是 file
		response.Response(w, resp, err)
	}
}
