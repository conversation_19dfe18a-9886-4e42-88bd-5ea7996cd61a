package team

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/team"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 邀请团队成员
func InviteTeamMemberHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.InviteTeamMemberReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := team.NewInviteTeamMemberLogic(r.Context(), svcCtx)
		resp, err := l.InviteTeamMember(&req)
		response.Response(w, resp, err)
	}
}
