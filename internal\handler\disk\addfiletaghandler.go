package disk

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/disk"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 为文件添加标签
func AddFileTagHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AddFileTagReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := disk.NewAddFileTagLogic(r.Context(), svcCtx)
		resp, err := l.AddFileTag(&req)
		response.Response(w, resp, err)
	}
}
