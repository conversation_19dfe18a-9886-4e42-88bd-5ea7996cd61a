package user

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/user"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 用户注册
func RegisterHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RegisterReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := user.NewRegisterLogic(r.Context(), svcCtx)
		resp, err := l.Register(&req)
		response.Response(w, resp, err)
	}
}
