package disk

import (
	"context"
	"errors"
	"fmt"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/model"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RestoreFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 恢复回收站文件
func NewRestoreFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RestoreFileLogic {
	return &RestoreFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// RestoreFile 从回收站恢复文件
func (l *RestoreFileLogic) RestoreFile(fileId string) (*types.BaseResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 查询文件是否存在
	file, err := l.svcCtx.FilesModel.FindOneByFileId(l.ctx, fileId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, errors.New("文件不存在")
		}
		return nil, fmt.Errorf("查询文件失败: %w", err)
	}

	// 检查文件是否属于当前用户
	if file.UserId != userId {
		return nil, errors.New("无权限操作此文件")
	}

	// 检查文件是否在回收站中
	if file.Status != 2 {
		return nil, errors.New("文件不在回收站中")
	}

	// 恢复文件（将状态改为正常）
	file.Status = 1
	err = l.svcCtx.FilesModel.Update(l.ctx, file)
	if err != nil {
		return nil, fmt.Errorf("恢复文件失败: %w", err)
	}

	return &types.BaseResp{
		Code:    200,
		Message: "文件恢复成功",
	}, nil
}
