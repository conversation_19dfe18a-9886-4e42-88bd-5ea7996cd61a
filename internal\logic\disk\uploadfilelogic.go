package disk

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"mime/multipart"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/internal/storage"
	"paper-editor-api/model"
	"path/filepath"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UploadFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 上传文件
func NewUploadFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadFileLogic {
	return &UploadFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UploadFileLogic) UploadFile(file *multipart.FileHeader, folderId string) (*types.FileInfo, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 生成文件ID
	fileId := fmt.Sprintf("file_%s", uuid.New().String()[:8])

	// 获取文件类型
	fileType := filepath.Ext(file.Filename)
	if fileType != "" && fileType[0] == '.' {
		fileType = fileType[1:] // 去掉开头的点
	}

	// 生成存储路径
	storagePath := storage.GenerateStoragePath(userId, fileId+fileType)

	// 创建存储实例
	fileStorage := storage.NewStorage(l.svcCtx.Config)

	// 保存文件
	path, err := fileStorage.SaveFile(file, storagePath)
	if err != nil {
		return nil, fmt.Errorf("保存文件失败: %w", err)
	}

	// 创建文件记录
	now := time.Now()
	fileModel := &model.Files{
		FileId:     fileId,
		FileName:   file.Filename,
		FileSize:   file.Size,
		FileType:   fileType,
		FolderId:   sql.NullString{String: folderId, Valid: folderId != ""},
		UserId:     userId,
		Path:       path,
		Status:     1, // 正常状态
		CreateTime: now,
		UpdateTime: now,
	}

	// 插入文件记录
	_, err = l.svcCtx.FilesModel.Insert(context.Background(), fileModel)
	if err != nil {
		// 如果数据库插入失败，尝试删除已上传的文件
		_ = fileStorage.DeleteFile(path)
		return nil, fmt.Errorf("保存文件记录失败: %w", err)
	}

	// 返回文件信息
	return &types.FileInfo{
		FileId:     fileModel.FileId,
		FileName:   fileModel.FileName,
		FileSize:   fileModel.FileSize,
		FileType:   fileModel.FileType,
		UploadTime: fileModel.CreateTime.Format(time.RFC3339),
		UpdateTime: fileModel.UpdateTime.Format(time.RFC3339),
		FolderId:   folderId,
	}, nil
}
