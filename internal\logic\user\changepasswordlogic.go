package user

import (
	"context"
	"errors"
	"golang.org/x/crypto/bcrypt"
	"paper-editor-api/internal/middleware"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangePasswordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 修改密码
func NewChangePasswordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangePasswordLogic {
	return &ChangePasswordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ChangePasswordLogic) ChangePassword(req *types.ChangePasswordReq) (bool, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return false, errors.New("获取用户信息失败")
	}

	// 查询用户信息
	user, err := l.svcCtx.UsersModel.FindOne(context.Background(), userId)
	if err != nil {
		return false, errors.New("用户不存在")
	}

	// 验证旧密码
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPassword))
	if err != nil {
		return false, errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return false, errors.New("密码加密失败")
	}

	// 更新密码
	user.Password = string(hashedPassword)
	err = l.svcCtx.UsersModel.Update(context.Background(), user)
	if err != nil {
		return false, errors.New("密码更新失败")
	}

	return true, nil
}
