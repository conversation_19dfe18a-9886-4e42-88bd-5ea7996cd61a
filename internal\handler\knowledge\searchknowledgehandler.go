package knowledge

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/knowledge"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 搜索知识点
func SearchKnowledgeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SearchKnowledgeReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := knowledge.NewSearchKnowledgeLogic(r.Context(), svcCtx)
		resp, err := l.SearchKnowledge(&req)
		response.Response(w, resp, err)
	}
}
