package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ KnowledgeModel = (*customKnowledgeModel)(nil)

type (
	// KnowledgeModel is an interface to be customized, add more methods here,
	// and implement the added methods in customKnowledgeModel.
	KnowledgeModel interface {
		knowledgeModel
		withSession(session sqlx.Session) KnowledgeModel
	}

	customKnowledgeModel struct {
		*defaultKnowledgeModel
	}
)

// NewKnowledgeModel returns a model for the database table.
func NewKnowledgeModel(conn sqlx.SqlConn) KnowledgeModel {
	return &customKnowledgeModel{
		defaultKnowledgeModel: newKnowledgeModel(conn),
	}
}

func (m *customKnowledgeModel) withSession(session sqlx.Session) KnowledgeModel {
	return NewKnowledgeModel(sqlx.NewSqlConnFromSession(session))
}
