package knowledge

import (
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/knowledge"
	"paper-editor-api/internal/svc"
)

// 删除知识点
func DeleteKnowledgeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := knowledge.NewDeleteKnowledgeLogic(r.Context(), svcCtx)
		resp, err := l.DeleteKnowledge()
		response.Response(w, resp, err)
	}
}
