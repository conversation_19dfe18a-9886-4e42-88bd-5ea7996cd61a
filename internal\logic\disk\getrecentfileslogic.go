package disk

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetRecentFilesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取最近访问文件列表
func NewGetRecentFilesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRecentFilesLogic {
	return &GetRecentFilesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// 获取最近访问文件列表
func (l *GetRecentFilesLogic) GetRecentFiles(req *types.FileListReq) (resp *types.FileListResp, err error) {

	var files []types.FileInfo
	var total int64

	files, total = l.svcCtx.Storage.GetRecentFiles(l.ctx, req.<PERSON>, req.PageSize)
	files = l.svcCtx.FilesModel.GetFileInfo(l.ctx, files)

	resp = &types.FileListResp{
		Total: total,
		List:  files,
	}

	return
}
