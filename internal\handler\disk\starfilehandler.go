package disk

import (
	"errors"
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/disk"
	"paper-editor-api/internal/svc"
)

// 标星文件
func StarFileHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		fileId := r.Path<PERSON>alue("fileId")
		if fileId == "" {
			response.Response(w, nil, errors.New("文件ID 不能为空"))
		}
		l := disk.NewStarFileLogic(r.Context(), svcCtx)
		resp, err := l.StarFile(fileId)
		response.Response(w, resp, err)
	}
}
