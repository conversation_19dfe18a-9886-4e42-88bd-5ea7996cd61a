package disk

import (
	"context"
	"errors"
	"fmt"
	"paper-editor-api/internal/middleware"
	"strconv"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTrashFilesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取回收站文件列表
func NewGetTrashFilesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTrashFilesLogic {
	return &GetTrashFilesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetTrashFiles 获取回收站文件列表
func (l *GetTrashFilesLogic) GetTrashFiles(req *types.FileListReq) (*types.FileListResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 验证分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20 // 默认每页20条
	}

	// 查询回收站文件（status = 2 表示已删除）
	files, err := l.svcCtx.FilesModel.FindTrashByUserId(l.ctx, userId, req.Page, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("查询回收站文件失败: %w", err)
	}

	// 查询回收站文件夹
	folderFiles, err := l.svcCtx.FoldersModel.FindTrashByUserId(l.ctx, userId, req.Page, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("查询回收站文件夹失败: %w", err)
	}

	// 统计文件总数
	fileTotal, err := l.svcCtx.FilesModel.CountTrashByUserId(l.ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("统计回收站文件数量失败: %w", err)
	}

	// 统计文件夹总数
	folderTotal, err := l.svcCtx.FoldersModel.CountTrashByUserId(l.ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("统计回收站文件夹数量失败: %w", err)
	}

	// 转换文件为响应格式
	var fileInfos []types.FileInfo
	for _, file := range files {
		fileInfo := types.FileInfo{
			FileId:     file.FileId,
			FileName:   file.FileName,
			FileSize:   file.FileSize,
			FileType:   file.FileType,
			UploadTime: file.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime: file.UpdateTime.Format("2006-01-02 15:04:05"),
			FolderId:   file.FolderId.String,
		}
		fileInfos = append(fileInfos, fileInfo)
	}

	// 转换文件夹为响应格式
	var folderInfos []types.FolderInfo
	for _, folder := range folderFiles {
		folderInfo := types.FolderInfo{
			FolderId:   folder.FolderId,
			FolderName: folder.FolderName,
			ParentId:   folder.ParentId.String,
			CreateTime: folder.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime: folder.UpdateTime.Format("2006-01-02 15:04:05"),
			// 注意：如果需要 DeleteTime、Description 和 Status 字段，
			// 需要确保 model.Folders 结构体中有这些字段
			Status: strconv.FormatInt(folder.Status, 10),
		}
		folderInfos = append(folderInfos, folderInfo)
	}

	return &types.FileListResp{
		Total:       fileTotal + folderTotal, // 总数为文件和文件夹的总和
		Files:       fileInfos,
		Folders:     folderInfos,
		FileCount:   fileTotal,   // 文件计数
		FolderCount: folderTotal, // 文件夹计数
	}, nil
}
