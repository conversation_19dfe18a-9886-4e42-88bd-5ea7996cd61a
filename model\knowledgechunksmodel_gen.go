// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	knowledgeChunksFieldNames          = builder.RawFieldNames(&KnowledgeChunks{}, true)
	knowledgeChunksRows                = strings.Join(knowledgeChunksFieldNames, ",")
	knowledgeChunksRowsExpectAutoSet   = strings.Join(stringx.Remove(knowledgeChunksFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	knowledgeChunksRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(knowledgeChunksFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	knowledgeChunksModel interface {
		Insert(ctx context.Context, data *KnowledgeChunks) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*KnowledgeChunks, error)
		FindOneByKnowledgeIdChunkIndex(ctx context.Context, knowledgeId string, chunkIndex int64) (*KnowledgeChunks, error)
		Update(ctx context.Context, data *KnowledgeChunks) error
		Delete(ctx context.Context, id int64) error
	}

	defaultKnowledgeChunksModel struct {
		conn  sqlx.SqlConn
		table string
	}

	KnowledgeChunks struct {
		Id           int64          `db:"id"`
		KnowledgeId  string         `db:"knowledge_id"`
		ChunkIndex   int64          `db:"chunk_index"`
		ChunkContent string         `db:"chunk_content"`
		EsDocId      sql.NullString `db:"es_doc_id"`
		CreateTime   time.Time      `db:"create_time"`
		UpdateTime   time.Time      `db:"update_time"`
	}
)

func newKnowledgeChunksModel(conn sqlx.SqlConn) *defaultKnowledgeChunksModel {
	return &defaultKnowledgeChunksModel{
		conn:  conn,
		table: `"public"."knowledge_chunks"`,
	}
}

func (m *defaultKnowledgeChunksModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultKnowledgeChunksModel) FindOne(ctx context.Context, id int64) (*KnowledgeChunks, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", knowledgeChunksRows, m.table)
	var resp KnowledgeChunks
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultKnowledgeChunksModel) FindOneByKnowledgeIdChunkIndex(ctx context.Context, knowledgeId string, chunkIndex int64) (*KnowledgeChunks, error) {
	var resp KnowledgeChunks
	query := fmt.Sprintf("select %s from %s where knowledge_id = $1 and chunk_index = $2 limit 1", knowledgeChunksRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, knowledgeId, chunkIndex)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultKnowledgeChunksModel) Insert(ctx context.Context, data *KnowledgeChunks) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4)", m.table, knowledgeChunksRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.KnowledgeId, data.ChunkIndex, data.ChunkContent, data.EsDocId)
	return ret, err
}

func (m *defaultKnowledgeChunksModel) Update(ctx context.Context, newData *KnowledgeChunks) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, knowledgeChunksRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.KnowledgeId, newData.ChunkIndex, newData.ChunkContent, newData.EsDocId)
	return err
}

func (m *defaultKnowledgeChunksModel) tableName() string {
	return m.table
}
