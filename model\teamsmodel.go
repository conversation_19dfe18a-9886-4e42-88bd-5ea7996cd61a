package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ TeamsModel = (*customTeamsModel)(nil)

type (
	// TeamsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTeamsModel.
	TeamsModel interface {
		teamsModel
		withSession(session sqlx.Session) TeamsModel
	}

	customTeamsModel struct {
		*defaultTeamsModel
	}
)

// NewTeamsModel returns a model for the database table.
func NewTeamsModel(conn sqlx.SqlConn) TeamsModel {
	return &customTeamsModel{
		defaultTeamsModel: newTeamsModel(conn),
	}
}

func (m *customTeamsModel) withSession(session sqlx.Session) TeamsModel {
	return NewTeamsModel(sqlx.NewSqlConnFromSession(session))
}
