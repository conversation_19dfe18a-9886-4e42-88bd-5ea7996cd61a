package user

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/user"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 验证重置密码
func VerifyResetPasswordHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.VerifyResetPasswordReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.Error(w, err)
			return
		}

		l := user.NewVerifyResetPasswordLogic(r.Context(), svcCtx)
		resp, err := l.VerifyResetPassword(&req)
		response.Response(w, resp, err)
	}
}
