// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	operationLogsFieldNames          = builder.RawFieldNames(&OperationLogs{}, true)
	operationLogsRows                = strings.Join(operationLogsFieldNames, ",")
	operationLogsRowsExpectAutoSet   = strings.Join(stringx.Remove(operationLogsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	operationLogsRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(operationLogsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	operationLogsModel interface {
		Insert(ctx context.Context, data *OperationLogs) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*OperationLogs, error)
		Update(ctx context.Context, data *OperationLogs) error
		Delete(ctx context.Context, id int64) error
	}

	defaultOperationLogsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	OperationLogs struct {
		Id           int64          `db:"id"`
		UserId       int64          `db:"user_id"`
		Action       string         `db:"action"`
		ResourceType string         `db:"resource_type"`
		ResourceId   sql.NullString `db:"resource_id"`
		Details      sql.NullString `db:"details"`
		IpAddress    sql.NullString `db:"ip_address"`
		UserAgent    sql.NullString `db:"user_agent"`
		CreateTime   time.Time      `db:"create_time"`
	}
)

func newOperationLogsModel(conn sqlx.SqlConn) *defaultOperationLogsModel {
	return &defaultOperationLogsModel{
		conn:  conn,
		table: `"public"."operation_logs"`,
	}
}

func (m *defaultOperationLogsModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultOperationLogsModel) FindOne(ctx context.Context, id int64) (*OperationLogs, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", operationLogsRows, m.table)
	var resp OperationLogs
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultOperationLogsModel) Insert(ctx context.Context, data *OperationLogs) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6, $7)", m.table, operationLogsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.UserId, data.Action, data.ResourceType, data.ResourceId, data.Details, data.IpAddress, data.UserAgent)
	return ret, err
}

func (m *defaultOperationLogsModel) Update(ctx context.Context, data *OperationLogs) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, operationLogsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Id, data.UserId, data.Action, data.ResourceType, data.ResourceId, data.Details, data.IpAddress, data.UserAgent)
	return err
}

func (m *defaultOperationLogsModel) tableName() string {
	return m.table
}
