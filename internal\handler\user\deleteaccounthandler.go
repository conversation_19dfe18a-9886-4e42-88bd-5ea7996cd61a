package user

import (
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/user"
	"paper-editor-api/internal/svc"
)

// 删除账户
func DeleteAccountHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := user.NewDeleteAccountLogic(r.Context(), svcCtx)
		resp, err := l.DeleteAccount()
		response.Response(w, resp, err)
	}
}
