package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ PaymentRecordsModel = (*customPaymentRecordsModel)(nil)

type (
	// PaymentRecordsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPaymentRecordsModel.
	PaymentRecordsModel interface {
		paymentRecordsModel
		withSession(session sqlx.Session) PaymentRecordsModel
	}

	customPaymentRecordsModel struct {
		*defaultPaymentRecordsModel
	}
)

// NewPaymentRecordsModel returns a model for the database table.
func NewPaymentRecordsModel(conn sqlx.SqlConn) PaymentRecordsModel {
	return &customPaymentRecordsModel{
		defaultPaymentRecordsModel: newPaymentRecordsModel(conn),
	}
}

func (m *customPaymentRecordsModel) withSession(session sqlx.Session) PaymentRecordsModel {
	return NewPaymentRecordsModel(sqlx.NewSqlConnFromSession(session))
}
