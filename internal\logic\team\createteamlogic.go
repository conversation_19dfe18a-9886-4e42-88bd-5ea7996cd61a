package team

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateTeamLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建团队
func NewCreateTeamLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateTeamLogic {
	return &CreateTeamLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateTeamLogic) CreateTeam(req *types.CreateTeamReq) (resp *types.CreateTeamResp, err error) {
	// todo: add your logic here and delete this line

	return
}
