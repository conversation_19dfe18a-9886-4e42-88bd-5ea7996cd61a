package user

import (
	"context"
	"errors"
	"golang.org/x/crypto/bcrypt"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type VerifyResetPasswordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 验证重置密码
func NewVerifyResetPasswordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *VerifyResetPasswordLogic {
	return &VerifyResetPasswordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *VerifyResetPasswordLogic) VerifyResetPassword(req *types.VerifyResetPasswordReq) (interface{}, error) {
	// 1. 验证令牌是否有效
	// 在实际应用中，应该从数据库或Redis中获取令牌对应的用户ID
	// 这里只是模拟，实际应用需要实现验证逻辑
	logx.Infof("验证重置密码令牌: %s", req.Token)

	// 模拟验证令牌
	if req.Token == "" {
		return nil, errors.New("无效的重置密码令牌")
	}

	// 2. 更新用户密码
	// 在实际应用中，应该根据令牌找到对应的用户，然后更新密码
	// 这里只是模拟，实际应用需要实现更新密码逻辑

	// 生成密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return nil, errors.New("密码处理失败")
	}

	logx.Infof("更新用户密码，新密码哈希: %s", string(hashedPassword))

	// 3. 删除使用过的令牌
	// 在实际应用中，应该从数据库或Redis中删除已使用的令牌
	// 这里只是模拟，实际应用需要实现删除令牌逻辑
	logx.Infof("删除已使用的重置密码令牌: %s", req.Token)

	return map[string]interface{}{
		"code":    200,
		"message": "密码重置成功",
	}, nil
}
