package disk

import (
	"context"
	"errors"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/model"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFilesByTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 根据标签获取文件列表
func NewGetFilesByTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFilesByTagLogic {
	return &GetFilesByTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetFilesByTag 根据标签名称获取用户关联的文件列表。
// 参数:
//   - req: 包含标签名称、分页信息的请求参数
//
// 返回值:
//   - resp: 文件列表响应，包含文件信息、总数等
//   - err: 错误信息，如果查询过程中发生错误则返回
func (l *GetFilesByTagLogic) GetFilesByTag(req *types.FileListReq) (resp *types.FileListResp, err error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 根据 tagName 查询 tagId
	tag, err := l.svcCtx.TagsModel.FindOneByTagName(l.ctx, req.TagName)
	if err != nil {
		return nil, err
	}

	// 查询文件
	files, err := l.svcCtx.FilesModel.FindByTag(l.ctx, userId, tag.Id, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	// 获取文件总数
	fileCount, err := l.svcCtx.FilesModel.CountByTag(l.ctx, userId, tag.Id)
	if err != nil {
		return nil, err
	}

	// 转换文件信息
	fileInfos := make([]types.FileInfo, len(files))
	for i, file := range files {
		// 查询文件是否被收藏
		starred, err := l.svcCtx.StarredFilesModel.FindByFileIdAndUserId(l.ctx, file.FileId, userId)
		var isStarred bool
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			return nil, err
		}
		isStarred = starred != nil

		// 查询文件的标签
		tagInfos, err := l.svcCtx.FileTagRelationsModel.FindTagsByFileId(l.ctx, file.FileId)
		if err != nil {
			return nil, err
		}
		var tags []string
		for _, tagInfo := range tagInfos {
			tags = append(tags, tagInfo.TagName)
		}

		fileInfos[i] = types.FileInfo{
			FileId:     file.FileId,
			FileName:   file.FileName,
			FileSize:   file.FileSize,
			FileType:   file.FileType,
			UploadTime: file.CreateTime.Format(time.DateTime),
			UpdateTime: file.UpdateTime.Format(time.DateTime),
			FolderId:   file.FolderId.String,
			IsStarred:  isStarred,
			Tags:       tags,
			Status:     int(file.Status),
		}
	}

	return &types.FileListResp{
		Total:       fileCount,
		Files:       fileInfos,
		Folders:     []types.FolderInfo{},
		FileCount:   fileCount,
		FolderCount: 0,
	}, nil
}
