{"swagger": "2.0", "info": {"title": "论文编辑器API服务", "description": "提供网盘功能和知识库管理功能的API服务", "version": "v1"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/disk/download/{fileId}": {"get": {"summary": "下载文件", "operationId": "downloadFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileDownloadResp"}}}, "parameters": [{"name": "fileId", "in": "path", "required": true, "type": "string"}], "tags": ["disk"]}}, "/api/v1/disk/file/tag": {"post": {"summary": "为文件添加标签", "operationId": "addFileTag", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "body", "description": " 添加文件标签请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/AddFileTagReq"}}], "tags": ["disk"]}}, "/api/v1/disk/file/{fileId}": {"get": {"summary": "获取文件详情", "operationId": "getFileDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileDetailResp"}}}, "parameters": [{"name": "fileId", "in": "path", "required": true, "type": "string"}], "tags": ["disk"]}, "delete": {"summary": "删除文件", "operationId": "deleteFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "fileId", "in": "path", "required": true, "type": "string"}], "tags": ["disk"]}}, "/api/v1/disk/file/{fileId}/tag/{tagId}": {"delete": {"summary": "移除文件标签", "operationId": "removeFileTag", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "fileId", "in": "path", "required": true, "type": "string"}, {"name": "tagId", "in": "path", "required": true, "type": "string"}], "tags": ["disk"]}}, "/api/v1/disk/files": {"get": {"summary": "获取文件列表", "operationId": "getFileList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileListResp"}}}, "parameters": [{"name": "folder_id", "in": "query", "required": false, "type": "string"}, {"name": "tag_name", "in": "query", "required": false, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "page_size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "20"}], "tags": ["disk"], "consumes": ["multipart/form-data"]}}, "/api/v1/disk/folder": {"post": {"summary": "创建文件夹", "operationId": "createFolder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CreateFolderResp"}}}, "parameters": [{"name": "body", "description": " 创建文件夹请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateFolderReq"}}], "tags": ["disk"]}}, "/api/v1/disk/move": {"put": {"summary": "移动文件", "operationId": "moveFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "body", "description": " 移动文件请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/MoveFileReq"}}], "tags": ["disk"]}}, "/api/v1/disk/recent": {"get": {"summary": "获取最近访问文件列表", "operationId": "getRecentFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileListResp"}}}, "parameters": [{"name": "folder_id", "in": "query", "required": false, "type": "string"}, {"name": "tag_name", "in": "query", "required": false, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "page_size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "20"}], "tags": ["disk"], "consumes": ["multipart/form-data"]}}, "/api/v1/disk/rename": {"put": {"summary": "重命名文件", "operationId": "renameFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "body", "description": " 重命名文件请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RenameFileReq"}}], "tags": ["disk"]}}, "/api/v1/disk/share": {"post": {"summary": "分享文件", "operationId": "shareFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ShareFileResp"}}}, "parameters": [{"name": "body", "description": " 分享文件请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ShareFileReq"}}], "tags": ["disk"]}}, "/api/v1/disk/shared": {"get": {"summary": "获取共享文件列表", "operationId": "getSharedFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileListResp"}}}, "parameters": [{"name": "folder_id", "in": "query", "required": false, "type": "string"}, {"name": "tag_name", "in": "query", "required": false, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "page_size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "20"}], "tags": ["disk"], "consumes": ["multipart/form-data"]}}, "/api/v1/disk/star/{fileId}": {"delete": {"summary": "取消标星文件", "operationId": "unstarFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "fileId", "in": "path", "required": true, "type": "string"}], "tags": ["disk"]}, "put": {"summary": "标星文件", "operationId": "starFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "fileId", "in": "path", "required": true, "type": "string"}], "tags": ["disk"]}}, "/api/v1/disk/starred": {"get": {"summary": "获取已标星文件列表", "operationId": "getStarredFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileListResp"}}}, "parameters": [{"name": "folder_id", "in": "query", "required": false, "type": "string"}, {"name": "tag_name", "in": "query", "required": false, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "page_size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "20"}], "tags": ["disk"], "consumes": ["multipart/form-data"]}}, "/api/v1/disk/tag/{tagId}/files": {"get": {"summary": "根据标签获取文件列表", "operationId": "getFilesByTag", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileListResp"}}}, "parameters": [{"name": "tagId", "in": "path", "required": true, "type": "string"}, {"name": "folder_id", "in": "query", "required": false, "type": "string"}, {"name": "tag_name", "in": "query", "required": false, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "page_size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "20"}], "tags": ["disk"], "consumes": ["multipart/form-data"]}}, "/api/v1/disk/tags": {"get": {"summary": "获取所有标签", "operationId": "getAllTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/TagListResp"}}}, "tags": ["disk"]}, "post": {"summary": "创建标签", "operationId": "createTag", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CreateTagResp"}}}, "parameters": [{"name": "body", "description": " 创建标签请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateTagReq"}}], "tags": ["disk"]}}, "/api/v1/disk/tags/{tagId}": {"delete": {"summary": "删除标签", "operationId": "deleteTag", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "tagId", "in": "path", "required": true, "type": "string"}], "tags": ["disk"]}}, "/api/v1/disk/trash": {"get": {"summary": "获取回收站文件列表", "operationId": "getTrashFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileListResp"}}}, "parameters": [{"name": "folder_id", "in": "query", "required": false, "type": "string"}, {"name": "tag_name", "in": "query", "required": false, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "page_size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "20"}], "tags": ["disk"], "consumes": ["multipart/form-data"]}}, "/api/v1/disk/trash/empty": {"delete": {"summary": "清空回收站", "operationId": "emptyTrash", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "tags": ["disk"]}}, "/api/v1/disk/trash/restore/{fileId}": {"put": {"summary": "恢复回收站文件", "operationId": "restoreFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "fileId", "in": "path", "required": true, "type": "string"}], "tags": ["disk"]}}, "/api/v1/disk/trash/{fileId}": {"delete": {"summary": "彻底删除文件", "operationId": "permanentDeleteFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "fileId", "in": "path", "required": true, "type": "string"}], "tags": ["disk"]}}, "/api/v1/disk/upload": {"post": {"summary": "上传文件", "operationId": "uploadFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileInfo"}}}, "tags": ["disk"]}}, "/api/v1/knowledge/add": {"post": {"summary": "添加知识点", "operationId": "addKnowledge", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/AddKnowledgeResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/AddKnowledgeReq"}}], "tags": ["knowledge"]}}, "/api/v1/knowledge/detail/{knowledgeId}": {"get": {"summary": "获取知识点详情", "operationId": "getKnowledgeDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/KnowledgeDetailResp"}}}, "parameters": [{"name": "knowledgeId", "in": "path", "required": true, "type": "string"}], "tags": ["knowledge"]}}, "/api/v1/knowledge/search": {"get": {"summary": "搜索知识点", "operationId": "searchKnowledge", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SearchKnowledgeResp"}}}, "parameters": [{"name": "keyword", "in": "query", "required": false, "type": "string"}, {"name": "tags", "in": "query", "required": false, "type": "string"}, {"name": "category_id", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "page_size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "20"}], "tags": ["knowledge"], "consumes": ["multipart/form-data"]}}, "/api/v1/knowledge/{knowledgeId}": {"delete": {"summary": "删除知识点", "operationId": "deleteKnowledge", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "knowledgeId", "in": "path", "required": true, "type": "string"}], "tags": ["knowledge"]}, "put": {"summary": "更新知识点", "operationId": "updateKnowledge", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "knowledgeId", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/AddKnowledgeReq"}}], "tags": ["knowledge"]}}, "/api/v1/team/create": {"post": {"summary": "创建团队", "operationId": "createTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CreateTeamResp"}}}, "parameters": [{"name": "body", "description": " 创建团队请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateTeamReq"}}], "tags": ["team"]}}, "/api/v1/team/invite": {"post": {"summary": "邀请团队成员", "operationId": "inviteTeamMember", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "body", "description": " 邀请团队成员请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/InviteTeamMemberReq"}}], "tags": ["team"]}}, "/api/v1/team/list": {"get": {"summary": "获取我的团队列表", "operationId": "getMyTeams", "responses": {"200": {"description": "A successful response.", "schema": {"type": "array", "items": {"$ref": "#/definitions/TeamInfo"}}}}, "tags": ["team"]}}, "/api/v1/team/{teamId}": {"get": {"summary": "获取团队详情", "operationId": "getTeamDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/TeamInfo"}}}, "parameters": [{"name": "teamId", "in": "path", "required": true, "type": "string"}], "tags": ["team"]}}, "/api/v1/team/{teamId}/files": {"get": {"summary": "获取团队文件列表", "operationId": "getTeamFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileListResp"}}}, "parameters": [{"name": "teamId", "in": "path", "required": true, "type": "string"}, {"name": "team_id", "in": "query", "required": true, "type": "string"}, {"name": "folder_id", "in": "query", "required": false, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "page_size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "20"}], "tags": ["team"], "consumes": ["multipart/form-data"]}}, "/api/v1/team/{teamId}/leave": {"delete": {"summary": "退出团队", "operationId": "leaveTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "teamId", "in": "path", "required": true, "type": "string"}], "tags": ["team"]}}, "/api/v1/team/{teamId}/member/{userId}": {"delete": {"summary": "移除团队成员", "operationId": "removeTeamMember", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "teamId", "in": "path", "required": true, "type": "string"}, {"name": "userId", "in": "path", "required": true, "type": "string"}], "tags": ["team"]}}, "/api/v1/team/{teamId}/members": {"get": {"summary": "获取团队成员列表", "operationId": "getTeamMembers", "responses": {"200": {"description": "A successful response.", "schema": {"type": "array", "items": {"$ref": "#/definitions/TeamMemberInfo"}}}}, "parameters": [{"name": "teamId", "in": "path", "required": true, "type": "string"}], "tags": ["team"]}}, "/api/v1/team/{teamId}/upload": {"post": {"summary": "上传团队文件", "operationId": "uploadTeamFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/FileInfo"}}}, "parameters": [{"name": "teamId", "in": "path", "required": true, "type": "string"}], "tags": ["team"]}}, "/api/v1/user/account": {"delete": {"summary": "删除账户", "operationId": "deleteAccount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "tags": ["user"]}}, "/api/v1/user/avatar": {"post": {"summary": "上传头像", "operationId": "uploadAvatar", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UploadAvatarResp"}}}, "tags": ["user"]}}, "/api/v1/user/info": {"get": {"summary": "获取用户信息", "operationId": "getUserInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UserInfoResp"}}}, "tags": ["user"]}, "put": {"summary": "更新用户信息", "operationId": "updateUserInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "body", "description": " 更新用户信息", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateUserInfoReq"}}], "tags": ["user"]}}, "/api/v1/user/login": {"post": {"summary": "用户登录", "operationId": "login", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/LoginResp"}}}, "parameters": [{"name": "body", "description": " 用户登录", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LoginReq"}}], "tags": ["user"]}}, "/api/v1/user/logout": {"post": {"summary": "用户退出登录", "operationId": "logout", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "tags": ["user"]}}, "/api/v1/user/password": {"put": {"summary": "修改密码", "operationId": "changePassword", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "body", "description": " 修改密码", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ChangePasswordReq"}}], "tags": ["user"]}}, "/api/v1/user/refresh": {"post": {"summary": "刷新令牌", "operationId": "refreshToken", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/RefreshTokenResp"}}}, "parameters": [{"name": "body", "description": " 刷新令牌", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RefreshTokenReq"}}], "tags": ["user"]}}, "/api/v1/user/register": {"post": {"summary": "用户注册", "operationId": "register", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/RegisterResp"}}}, "parameters": [{"name": "body", "description": " 用户注册", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RegisterReq"}}], "tags": ["user"]}}, "/api/v1/user/reset-password": {"post": {"summary": "重置密码请求", "operationId": "resetPassword", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ResetPasswordResp"}}}, "parameters": [{"name": "body", "description": " 重置密码", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ResetPasswordReq"}}], "tags": ["user"]}}, "/api/v1/user/verify-reset-password": {"post": {"summary": "验证重置密码", "operationId": "verifyResetPassword", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/BaseResp"}}}, "parameters": [{"name": "body", "description": " 验证重置密码", "in": "body", "required": true, "schema": {"$ref": "#/definitions/VerifyResetPasswordReq"}}], "tags": ["user"]}}}, "definitions": {"AddFileTagReq": {"type": "object", "properties": {"file_id": {"type": "string"}, "tag_id": {"type": "integer", "format": "int64"}}, "title": "AddFileTagReq", "required": ["file_id", "tag_id"]}, "AddKnowledgeReq": {"type": "object", "properties": {"title": {"type": "string"}, "content": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "category_id": {"type": "integer", "format": "int64"}}, "title": "AddKnowledgeReq", "required": ["title", "content"]}, "AddKnowledgeResp": {"type": "object", "properties": {"knowledge_id": {"type": "string"}}, "title": "AddKnowledgeResp", "required": ["knowledge_id"]}, "BaseResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}}, "title": "BaseResp", "required": ["code", "message"]}, "ChangePasswordReq": {"type": "object", "properties": {"old_password": {"type": "string"}, "new_password": {"type": "string"}}, "title": "ChangePasswordReq", "required": ["old_password", "new_password"]}, "CreateFolderReq": {"type": "object", "properties": {"folder_name": {"type": "string"}, "parent_id": {"type": "string"}}, "title": "CreateFolderReq", "required": ["folder_name"]}, "CreateFolderResp": {"type": "object", "properties": {"folder_id": {"type": "string"}}, "title": "CreateFolderResp", "required": ["folder_id"]}, "CreateTagReq": {"type": "object", "properties": {"tag_name": {"type": "string"}, "color": {"type": "string"}}, "title": "CreateTagReq", "required": ["tag_name"]}, "CreateTagResp": {"type": "object", "properties": {"tag_id": {"type": "integer", "format": "int64"}}, "title": "CreateTagResp", "required": ["tag_id"]}, "CreateTeamReq": {"type": "object", "properties": {"team_name": {"type": "string"}, "description": {"type": "string"}}, "title": "CreateTeamReq", "required": ["team_name"]}, "CreateTeamResp": {"type": "object", "properties": {"team_id": {"type": "string"}}, "title": "CreateTeamResp", "required": ["team_id"]}, "FileDetailResp": {"type": "object", "properties": {"file_info": {"$ref": "#/definitions/FileInfo"}, "url": {"type": "string"}, "expires": {"type": "integer", "format": "int64"}}, "title": "FileDetailResp", "required": ["file_info", "url", "expires"]}, "FileDownloadResp": {"type": "object", "title": "FileDownloadResp"}, "FileInfo": {"type": "object", "properties": {"file_id": {"type": "string"}, "file_name": {"type": "string"}, "file_size": {"type": "integer", "format": "int64"}, "file_type": {"type": "string"}, "upload_time": {"type": "string"}, "update_time": {"type": "string"}, "folder_id": {"type": "string"}, "is_starred": {"type": "boolean", "format": "boolean"}, "tags": {"type": "array", "items": {"type": "string"}}, "status": {"type": "integer", "format": "int32", "description": " 1:正常 2:回收站 3:已删除"}}, "title": "FileInfo", "required": ["file_id", "file_name", "file_size", "file_type", "upload_time", "update_time", "folder_id", "is_starred", "tags", "status"]}, "FileListReq": {"type": "object", "properties": {"folder_id": {"type": "string"}, "tag_name": {"type": "string"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "page_size": {"type": "integer", "format": "int32", "default": "20"}}, "title": "FileListReq", "required": ["page", "page_size"]}, "FileListResp": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "files": {"type": "array", "items": {"$ref": "#/definitions/FileInfo"}}, "folders": {"type": "array", "items": {"$ref": "#/definitions/FolderInfo"}}, "file_count": {"type": "integer", "format": "int64"}, "folder_count": {"type": "integer", "format": "int64"}}, "title": "FileListResp", "required": ["total", "files", "folders", "file_count", "folder_count"]}, "FolderInfo": {"type": "object", "properties": {"folder_id": {"type": "string"}, "folder_name": {"type": "string"}, "parent_id": {"type": "string"}, "create_time": {"type": "string"}, "update_time": {"type": "string"}, "status": {"type": "string"}}, "title": "FolderInfo", "required": ["folder_id", "folder_name", "parent_id", "create_time", "update_time", "status"]}, "InviteTeamMemberReq": {"type": "object", "properties": {"team_id": {"type": "string"}, "username": {"type": "string"}, "role": {"type": "string", "default": "member"}}, "title": "InviteTeamMemberReq", "required": ["team_id", "username", "role"]}, "KnowledgeDetailResp": {"type": "object", "properties": {"knowledge_id": {"type": "string"}, "title": {"type": "string"}, "content": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "category_id": {"type": "integer", "format": "int64"}, "category": {"type": "string"}, "create_time": {"type": "string"}, "update_time": {"type": "string"}}, "title": "KnowledgeDetailResp", "required": ["knowledge_id", "title", "content", "tags", "category_id", "category", "create_time", "update_time"]}, "KnowledgeInfo": {"type": "object", "properties": {"knowledge_id": {"type": "string"}, "title": {"type": "string"}, "content_preview": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "category": {"type": "string"}, "create_time": {"type": "string"}, "update_time": {"type": "string"}}, "title": "KnowledgeInfo", "required": ["knowledge_id", "title", "content_preview", "tags", "category", "create_time", "update_time"]}, "LoginReq": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "title": "LoginReq", "required": ["username", "password"]}, "LoginResp": {"type": "object", "properties": {"access_token": {"type": "string"}, "refresh_token": {"type": "string"}, "expires": {"type": "integer", "format": "int64"}}, "title": "LoginResp", "required": ["access_token", "refresh_token", "expires"]}, "MoveFileReq": {"type": "object", "properties": {"file_id": {"type": "string"}, "folder_id": {"type": "string"}}, "title": "MoveFileReq", "required": ["file_id", "folder_id"]}, "RefreshTokenReq": {"type": "object", "properties": {"refresh_token": {"type": "string"}}, "title": "RefreshTokenReq", "required": ["refresh_token"]}, "RefreshTokenResp": {"type": "object", "properties": {"access_token": {"type": "string"}, "expires": {"type": "integer", "format": "int64"}}, "title": "RefreshTokenResp", "required": ["access_token", "expires"]}, "RegisterReq": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "email": {"type": "string"}, "nickname": {"type": "string"}}, "title": "RegisterReq", "required": ["username", "password"]}, "RegisterResp": {"type": "object", "properties": {"user_id": {"type": "string"}}, "title": "RegisterResp", "required": ["user_id"]}, "RenameFileReq": {"type": "object", "properties": {"file_id": {"type": "string"}, "file_name": {"type": "string"}}, "title": "RenameFileReq", "required": ["file_id", "file_name"]}, "ResetPasswordReq": {"type": "object", "properties": {"email": {"type": "string"}}, "title": "ResetPasswordReq", "required": ["email"]}, "ResetPasswordResp": {"type": "object", "properties": {"message": {"type": "string"}}, "title": "ResetPasswordResp", "required": ["message"]}, "SearchKnowledgeReq": {"type": "object", "properties": {"keyword": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "category_id": {"type": "integer", "format": "int64"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "page_size": {"type": "integer", "format": "int32", "default": "20"}}, "title": "SearchKnowledgeReq", "required": ["page", "page_size"]}, "SearchKnowledgeResp": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/definitions/KnowledgeInfo"}}}, "title": "SearchKnowledgeResp", "required": ["total", "items"]}, "ShareFileReq": {"type": "object", "properties": {"file_id": {"type": "string"}, "expire_days": {"type": "integer", "format": "int32", "description": " 过期天数，0表示永不过期"}, "password": {"type": "string", "description": " 分享密码"}}, "title": "ShareFileReq", "required": ["file_id"]}, "ShareFileResp": {"type": "object", "properties": {"share_id": {"type": "string"}, "share_url": {"type": "string"}, "password": {"type": "string"}, "expire_time": {"type": "string"}}, "title": "ShareFileResp", "required": ["share_id", "share_url"]}, "TagInfo": {"type": "object", "properties": {"tag_id": {"type": "integer", "format": "int64"}, "tag_name": {"type": "string"}, "color": {"type": "string"}, "file_count": {"type": "integer", "format": "int64"}}, "title": "TagInfo", "required": ["tag_id", "tag_name", "color", "file_count"]}, "TagListResp": {"type": "object", "properties": {"tags": {"type": "array", "items": {"$ref": "#/definitions/TagInfo"}}}, "title": "TagListResp", "required": ["tags"]}, "TeamFileListReq": {"type": "object", "properties": {"team_id": {"type": "string"}, "folder_id": {"type": "string"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "page_size": {"type": "integer", "format": "int32", "default": "20"}}, "title": "TeamFileListReq", "required": ["team_id", "page", "page_size"]}, "TeamInfo": {"type": "object", "properties": {"team_id": {"type": "string"}, "team_name": {"type": "string"}, "description": {"type": "string"}, "owner_id": {"type": "integer", "format": "int64"}, "member_count": {"type": "integer", "format": "int32"}, "create_time": {"type": "string"}}, "title": "TeamInfo", "required": ["team_id", "team_name", "description", "owner_id", "member_count", "create_time"]}, "TeamMemberInfo": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "nickname": {"type": "string"}, "role": {"type": "string", "description": " owner, admin, member"}, "join_time": {"type": "string"}}, "title": "TeamMemberInfo", "required": ["user_id", "username", "nickname", "role", "join_time"]}, "UpdateUserInfoReq": {"type": "object", "properties": {"nickname": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}}, "title": "UpdateUserInfoReq"}, "UploadAvatarResp": {"type": "object", "properties": {"avatar_url": {"type": "string"}}, "title": "UploadAvatarResp", "required": ["avatar_url"]}, "UserInfoResp": {"type": "object", "properties": {"user_id": {"type": "string"}, "username": {"type": "string"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "membership_type": {"type": "string"}, "storage_quota": {"type": "integer", "format": "int64"}, "knowledge_quota": {"type": "integer", "format": "int64"}, "create_time": {"type": "string"}}, "title": "UserInfoResp", "required": ["user_id", "username", "nickname", "avatar", "email", "phone", "membership_type", "storage_quota", "knowledge_quota", "create_time"]}, "VerifyResetPasswordReq": {"type": "object", "properties": {"token": {"type": "string"}, "new_password": {"type": "string"}}, "title": "VerifyResetPasswordReq", "required": ["token", "new_password"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}}