package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ KnowledgeChunksModel = (*customKnowledgeChunksModel)(nil)

type (
	// KnowledgeChunksModel is an interface to be customized, add more methods here,
	// and implement the added methods in customKnowledgeChunksModel.
	KnowledgeChunksModel interface {
		knowledgeChunksModel
		withSession(session sqlx.Session) KnowledgeChunksModel
	}

	customKnowledgeChunksModel struct {
		*defaultKnowledgeChunksModel
	}
)

// NewKnowledgeChunksModel returns a model for the database table.
func NewKnowledgeChunksModel(conn sqlx.SqlConn) KnowledgeChunksModel {
	return &customKnowledgeChunksModel{
		defaultKnowledgeChunksModel: newKnowledgeChunksModel(conn),
	}
}

func (m *customKnowledgeChunksModel) withSession(session sqlx.Session) KnowledgeChunksModel {
	return NewKnowledgeChunksModel(sqlx.NewSqlConnFromSession(session))
}
