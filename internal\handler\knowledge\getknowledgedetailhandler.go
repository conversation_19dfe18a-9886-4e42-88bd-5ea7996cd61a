package knowledge

import (
	"errors"
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/knowledge"
	"paper-editor-api/internal/svc"
)

// 获取知识点详情
func GetKnowledgeDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {

		// 从URL路径中获取知识点ID
		knowledgeId := r.PathValue("knowledgeId")
		if knowledgeId == "" {
			response.Response(w, nil, errors.New("知识点ID 不能为空"))
			return
		}
		l := knowledge.NewGetKnowledgeDetailLogic(r.Context(), svcCtx)
		resp, err := l.GetKnowledgeDetail(knowledgeId)
		response.Response(w, resp, err)
	}
}
