package model

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ FileTagsModel = (*customFileTagsModel)(nil)

type (
	// FileTagsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFileTagsModel.
	FileTagsModel interface {
		fileTagsModel
		withSession(session sqlx.Session) FileTagsModel
		FindByTagName(ctx context.Context, tagName string, userId int64) (*FileTags, error)
		FindByUserId(ctx context.Context, userId int64) ([]FileTags, error)
		FindByIds(ctx context.Context, ids []int64) ([]FileTags, error)
	}

	customFileTagsModel struct {
		*defaultFileTagsModel
	}
)

// NewFileTagsModel returns a model for the database table.
func NewFileTagsModel(conn sqlx.SqlConn) FileTagsModel {
	return &customFileTagsModel{
		defaultFileTagsModel: newFileTagsModel(conn),
	}
}

func (m *customFileTagsModel) withSession(session sqlx.Session) FileTagsModel {
	return NewFileTagsModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customFileTagsModel) FindByTagName(ctx context.Context, tagName string, userId int64) (*FileTags, error) {
	query := fmt.Sprintf("select %s from %s where tag_name = ? and user_id = ?", fileTagsRows, m.table)
	var resp FileTags
	err := m.conn.QueryRowCtx(ctx, &resp, query, tagName, userId)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

func (m *customFileTagsModel) FindByUserId(ctx context.Context, userId int64) ([]FileTags, error) {
	query := fmt.Sprintf("select %s from %s where user_id = ?", fileTagsRows, m.table)
	var resp []FileTags
	err := m.conn.QueryRowCtx(ctx, &resp, query, userId)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *customFileTagsModel) FindByIds(ctx context.Context, ids []int64) ([]FileTags, error) {
	query := fmt.Sprintf("select %s from %s where id in (?)", fileTagsRows, m.table)
	var resp []FileTags
	err := m.conn.QueryRowCtx(ctx, &resp, query, ids)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
