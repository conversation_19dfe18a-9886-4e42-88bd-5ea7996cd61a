package disk

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/disk"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 重命名文件
func RenameFileHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RenameFileReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := disk.NewRenameFileLogic(r.Context(), svcCtx)
		resp, err := l.RenameFile(&req)
		response.Response(w, resp, err)
	}
}
