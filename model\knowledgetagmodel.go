package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ KnowledgeTagModel = (*customKnowledgeTagModel)(nil)

type (
	// KnowledgeTagModel is an interface to be customized, add more methods here,
	// and implement the added methods in customKnowledgeTagModel.
	KnowledgeTagModel interface {
		knowledgeTagModel
		withSession(session sqlx.Session) KnowledgeTagModel
	}

	customKnowledgeTagModel struct {
		*defaultKnowledgeTagModel
	}
)

// NewKnowledgeTagModel returns a model for the database table.
func NewKnowledgeTagModel(conn sqlx.SqlConn) KnowledgeTagModel {
	return &customKnowledgeTagModel{
		defaultKnowledgeTagModel: newKnowledgeTagModel(conn),
	}
}

func (m *customKnowledgeTagModel) withSession(session sqlx.Session) KnowledgeTagModel {
	return NewKnowledgeTagModel(sqlx.NewSqlConnFromSession(session))
}
