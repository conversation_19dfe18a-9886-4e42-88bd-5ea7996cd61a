package knowledge

import (
	"context"
	"errors"
	"paper-editor-api/internal/middleware"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetKnowledgeDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取知识点详情
func NewGetKnowledgeDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetKnowledgeDetailLogic {
	return &GetKnowledgeDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetKnowledgeDetailLogic) GetKnowledgeDetail(knowledgeId string) (*types.KnowledgeDetailResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 查询知识点
	knowledge, err := l.svcCtx.KnowledgeModel.FindOneByKnowledgeId(context.Background(), knowledgeId)
	if err != nil {
		return nil, errors.New("知识点不存在")
	}

	// 检查是否是当前用户的知识点
	if knowledge.UserId != userId {
		return nil, errors.New("无权访问该知识点")
	}

	// TODO: 获取标签功能需要自定义实现
	// 由于 goctl 生成的模型没有 GetTags 方法，这里临时返回空标签
	tagNames := []string{}

	// 处理可能为空的分类ID
	categoryId := int64(0)
	if knowledge.CategoryId.Valid {
		categoryId = knowledge.CategoryId.Int64
	}

	// 返回知识点详情
	return &types.KnowledgeDetailResp{
		KnowledgeId: knowledge.KnowledgeId,
		Title:       knowledge.Title,
		Content:     knowledge.Content,
		Tags:        tagNames,
		CategoryId:  categoryId,
		Category:    "", // 这里应该查询分类名称
		CreateTime:  knowledge.CreateTime.Format(time.RFC3339),
		UpdateTime:  knowledge.UpdateTime.Format(time.RFC3339),
	}, nil
}
