package disk

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/disk"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 创建文件夹
func CreateFolderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateFolderReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := disk.NewCreateFolderLogic(r.Context(), svcCtx)
		resp, err := l.CreateFolder(&req)
		response.Response(w, resp, err)
	}
}
