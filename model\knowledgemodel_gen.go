// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	knowledgeFieldNames          = builder.RawFieldNames(&Knowledge{}, true)
	knowledgeRows                = strings.Join(knowledgeFieldNames, ",")
	knowledgeRowsExpectAutoSet   = strings.Join(stringx.Remove(knowledgeFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	knowledgeRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(knowledgeFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	knowledgeModel interface {
		Insert(ctx context.Context, data *Knowledge) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Knowledge, error)
		FindOneByKnowledgeId(ctx context.Context, knowledgeId string) (*Knowledge, error)
		Update(ctx context.Context, data *Knowledge) error
		Delete(ctx context.Context, id int64) error
	}

	defaultKnowledgeModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Knowledge struct {
		Id          int64         `db:"id"`
		KnowledgeId string        `db:"knowledge_id"`
		Title       string        `db:"title"`
		Content     string        `db:"content"`
		CategoryId  sql.NullInt64 `db:"category_id"`
		UserId      int64         `db:"user_id"`
		Status      int64         `db:"status"`
		CreateTime  time.Time     `db:"create_time"`
		UpdateTime  time.Time     `db:"update_time"`
	}
)

func newKnowledgeModel(conn sqlx.SqlConn) *defaultKnowledgeModel {
	return &defaultKnowledgeModel{
		conn:  conn,
		table: `"public"."knowledge"`,
	}
}

func (m *defaultKnowledgeModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultKnowledgeModel) FindOne(ctx context.Context, id int64) (*Knowledge, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", knowledgeRows, m.table)
	var resp Knowledge
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultKnowledgeModel) FindOneByKnowledgeId(ctx context.Context, knowledgeId string) (*Knowledge, error) {
	var resp Knowledge
	query := fmt.Sprintf("select %s from %s where knowledge_id = $1 limit 1", knowledgeRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, knowledgeId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultKnowledgeModel) Insert(ctx context.Context, data *Knowledge) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6)", m.table, knowledgeRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.KnowledgeId, data.Title, data.Content, data.CategoryId, data.UserId, data.Status)
	return ret, err
}

func (m *defaultKnowledgeModel) Update(ctx context.Context, newData *Knowledge) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, knowledgeRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.KnowledgeId, newData.Title, newData.Content, newData.CategoryId, newData.UserId, newData.Status)
	return err
}

func (m *defaultKnowledgeModel) tableName() string {
	return m.table
}
