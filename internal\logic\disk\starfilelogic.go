package disk

import (
	"context"
	"errors"
	"fmt"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/model"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type StarFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 标星文件
func NewStarFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *StarFileLogic {
	return &StarFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// StarFile 标星文件
func (l *StarFileLogic) StarFile(fileId string) (*types.BaseResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 检查文件是否存在
	file, err := l.svcCtx.FilesModel.FindOneByFileId(l.ctx, fileId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, errors.New("文件不存在")
		}
		return nil, fmt.Errorf("查询文件失败: %w", err)
	}

	// 检查文件是否属于当前用户
	if file.UserId != userId {
		return nil, errors.New("无权限操作此文件")
	}

	// 检查是否已经标星
	existingStar, err := l.svcCtx.StarredFilesModel.FindByFileIdAndUserId(l.ctx, fileId, userId)
	if err != nil && err != model.ErrNotFound {
		return nil, fmt.Errorf("检查标星状态失败: %w", err)
	}
	if existingStar != nil {
		return &types.BaseResp{
			Code:    200,
			Message: "文件已经标星",
		}, nil
	}

	// 创建标星记录
	starModel := &model.StarredFiles{
		FileId: fileId,
		UserId: userId,
	}

	_, err = l.svcCtx.StarredFilesModel.Insert(l.ctx, starModel)
	if err != nil {
		return nil, fmt.Errorf("标星文件失败: %w", err)
	}

	return &types.BaseResp{
		Code:    200,
		Message: "标星成功",
	}, nil
}

// UnstarFile 取消标星文件
func (l *StarFileLogic) UnstarFile(fileId string) (*types.BaseResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 检查标星记录是否存在
	starRecord, err := l.svcCtx.StarredFilesModel.FindByFileIdAndUserId(l.ctx, fileId, userId)
	if err != nil {
		if err == model.ErrNotFound {
			return &types.BaseResp{
				Code:    200,
				Message: "文件未标星",
			}, nil
		}
		return nil, fmt.Errorf("查询标星记录失败: %w", err)
	}

	// 删除标星记录
	err = l.svcCtx.StarredFilesModel.Delete(l.ctx, starRecord.Id)
	if err != nil {
		return nil, fmt.Errorf("取消标星失败: %w", err)
	}

	return &types.BaseResp{
		Code:    200,
		Message: "取消标星成功",
	}, nil
}
