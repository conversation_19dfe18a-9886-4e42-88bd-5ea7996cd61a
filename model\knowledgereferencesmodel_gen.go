// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	knowledgeReferencesFieldNames          = builder.RawFieldNames(&KnowledgeReferences{}, true)
	knowledgeReferencesRows                = strings.Join(knowledgeReferencesFieldNames, ",")
	knowledgeReferencesRowsExpectAutoSet   = strings.Join(stringx.Remove(knowledgeReferencesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	knowledgeReferencesRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(knowledgeReferencesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	knowledgeReferencesModel interface {
		Insert(ctx context.Context, data *KnowledgeReferences) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*KnowledgeReferences, error)
		FindOneBySourceIdTargetId(ctx context.Context, sourceId string, targetId string) (*KnowledgeReferences, error)
		Update(ctx context.Context, data *KnowledgeReferences) error
		Delete(ctx context.Context, id int64) error
	}

	defaultKnowledgeReferencesModel struct {
		conn  sqlx.SqlConn
		table string
	}

	KnowledgeReferences struct {
		Id         int64     `db:"id"`
		SourceId   string    `db:"source_id"`
		TargetId   string    `db:"target_id"`
		CreateTime time.Time `db:"create_time"`
	}
)

func newKnowledgeReferencesModel(conn sqlx.SqlConn) *defaultKnowledgeReferencesModel {
	return &defaultKnowledgeReferencesModel{
		conn:  conn,
		table: `"public"."knowledge_references"`,
	}
}

func (m *defaultKnowledgeReferencesModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultKnowledgeReferencesModel) FindOne(ctx context.Context, id int64) (*KnowledgeReferences, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", knowledgeReferencesRows, m.table)
	var resp KnowledgeReferences
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultKnowledgeReferencesModel) FindOneBySourceIdTargetId(ctx context.Context, sourceId string, targetId string) (*KnowledgeReferences, error) {
	var resp KnowledgeReferences
	query := fmt.Sprintf("select %s from %s where source_id = $1 and target_id = $2 limit 1", knowledgeReferencesRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, sourceId, targetId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultKnowledgeReferencesModel) Insert(ctx context.Context, data *KnowledgeReferences) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2)", m.table, knowledgeReferencesRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.SourceId, data.TargetId)
	return ret, err
}

func (m *defaultKnowledgeReferencesModel) Update(ctx context.Context, newData *KnowledgeReferences) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, knowledgeReferencesRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.SourceId, newData.TargetId)
	return err
}

func (m *defaultKnowledgeReferencesModel) tableName() string {
	return m.table
}
