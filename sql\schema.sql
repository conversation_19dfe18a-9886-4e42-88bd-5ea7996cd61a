-- 论文编辑器API服务数据库表结构
-- PostgreSQL版本

-- 会员类型表
CREATE TABLE IF NOT EXISTS membership_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    priority INT NOT NULL, -- 优先级，数字越大权限越高
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 初始化会员类型
INSERT INTO membership_types (name, description, priority) VALUES
('普通用户', '基础功能访问权限', 0),
('月度会员', '每月付费会员，拥有更多功能和资源', 10),
('季度会员', '每季度付费会员，拥有更多功能和资源', 20),
('年度会员', '每年付费会员，拥有更多功能和资源', 30),
('超级会员', '永久会员，拥有全部功能和最高资源配额', 40),
('机构会员', '面向机构的会员类型，可包含多个子账号', 50)
ON CONFLICT (name) DO NOTHING;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL UNIQUE,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(128) NOT NULL,
    nickname VARCHAR(50),
    avatar VARCHAR(255),
    email VARCHAR(100),
    phone VARCHAR(20),
    membership_type_id INT NOT NULL DEFAULT 1, -- 默认为普通用户
    membership_expire_time TIMESTAMP, -- 会员过期时间，NULL表示永久或非会员
    storage_quota BIGINT DEFAULT 1073741824, -- 存储配额，默认1GB
    knowledge_quota INT DEFAULT 100, -- 知识库条目配额
    parent_user_id INT, -- 父用户ID，用于机构会员的子账号
    status SMALLINT NOT NULL DEFAULT 1, -- 1: 正常, 0: 禁用
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (membership_type_id) REFERENCES membership_types(id),
    FOREIGN KEY (parent_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 文件夹表
CREATE TABLE IF NOT EXISTS folders (
    id SERIAL PRIMARY KEY,
    folder_id VARCHAR(64) NOT NULL UNIQUE,
    folder_name VARCHAR(255) NOT NULL,
    parent_id VARCHAR(64),
    user_id INT NOT NULL,
    status SMALLINT NOT NULL DEFAULT 1, -- 1: 正常, 0: 删除
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 文件表
CREATE TABLE IF NOT EXISTS files (
    id SERIAL PRIMARY KEY,
    file_id VARCHAR(64) NOT NULL UNIQUE,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    folder_id VARCHAR(64),
    user_id INT NOT NULL,
    path VARCHAR(512) NOT NULL,
    status SMALLINT NOT NULL DEFAULT 1, -- 1: 正常, 0: 删除
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 文件版本表
CREATE TABLE IF NOT EXISTS file_versions (
    id SERIAL PRIMARY KEY,
    file_id VARCHAR(64) NOT NULL,
    version INT NOT NULL,
    file_size BIGINT NOT NULL,
    path VARCHAR(512) NOT NULL,
    remark VARCHAR(255),
    user_id INT NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES files(file_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE (file_id, version)
);

-- 文件分享表
CREATE TABLE IF NOT EXISTS file_shares (
    id SERIAL PRIMARY KEY,
    share_id VARCHAR(64) NOT NULL UNIQUE,
    file_id VARCHAR(64) NOT NULL,
    user_id INT NOT NULL,
    expire_time TIMESTAMP,
    access_count INT NOT NULL DEFAULT 0,
    status SMALLINT NOT NULL DEFAULT 1, -- 1: 有效, 0: 无效
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES files(file_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 知识分类表
CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    user_id INT NOT NULL,
    parent_id INT,
    status SMALLINT NOT NULL DEFAULT 1, -- 1: 正常, 0: 删除
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- 标签表
CREATE TABLE IF NOT EXISTS tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    user_id INT NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE (name, user_id)
);

-- 知识点表
CREATE TABLE IF NOT EXISTS knowledge (
    id SERIAL PRIMARY KEY,
    knowledge_id VARCHAR(64) NOT NULL UNIQUE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category_id INT,
    user_id INT NOT NULL,
    status SMALLINT NOT NULL DEFAULT 1, -- 1: 正常, 0: 删除
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 知识点标签关联表
CREATE TABLE IF NOT EXISTS knowledge_tag (
    id SERIAL PRIMARY KEY,
    knowledge_id VARCHAR(64) NOT NULL,
    tag_id INT NOT NULL,
    FOREIGN KEY (knowledge_id) REFERENCES knowledge(knowledge_id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    UNIQUE (knowledge_id, tag_id)
);

-- 知识点引用表（知识点之间的关联）
CREATE TABLE IF NOT EXISTS knowledge_references (
    id SERIAL PRIMARY KEY,
    source_id VARCHAR(64) NOT NULL,
    target_id VARCHAR(64) NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_id) REFERENCES knowledge(knowledge_id) ON DELETE CASCADE,
    FOREIGN KEY (target_id) REFERENCES knowledge(knowledge_id) ON DELETE CASCADE,
    UNIQUE (source_id, target_id)
);

-- 创建索引
CREATE INDEX idx_files_folder_id ON files(folder_id);
CREATE INDEX idx_files_user_id ON files(user_id);
CREATE INDEX idx_knowledge_category_id ON knowledge(category_id);
CREATE INDEX idx_knowledge_user_id ON knowledge(user_id);
CREATE INDEX idx_knowledge_tag_knowledge_id ON knowledge_tag(knowledge_id);
CREATE INDEX idx_knowledge_tag_tag_id ON knowledge_tag(tag_id);

-- 知识点向量表（用于RAG）
-- 注意：实际的向量数据将存储在Elasticsearch中
CREATE TABLE IF NOT EXISTS knowledge_chunks (
    id SERIAL PRIMARY KEY,
    knowledge_id VARCHAR(64) NOT NULL,
    chunk_index INT NOT NULL, -- 分块索引，一个知识点可能被分成多个块
    chunk_content TEXT NOT NULL, -- 分块内容
    es_doc_id VARCHAR(64), -- Elasticsearch文档ID
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_id) REFERENCES knowledge(knowledge_id) ON DELETE CASCADE,
    UNIQUE (knowledge_id, chunk_index)
);

-- 创建索引
CREATE INDEX idx_knowledge_chunks_knowledge_id ON knowledge_chunks(knowledge_id);
CREATE INDEX idx_knowledge_chunks_es_doc_id ON knowledge_chunks(es_doc_id);

-- Elasticsearch配置表
CREATE TABLE IF NOT EXISTS elasticsearch_config (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(64) NOT NULL,
    description TEXT,
    settings JSONB,
    mappings JSONB,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 初始化Elasticsearch配置
INSERT INTO elasticsearch_config (index_name, description, settings, mappings) VALUES
('knowledge_vectors', '知识库向量索引', 
 '{
    "number_of_shards": 1,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "text_analyzer": {
          "type": "custom",
          "tokenizer": "standard",
          "filter": ["lowercase", "asciifolding"]
        }
      }
    }
  }',
 '{
    "properties": {
      "knowledge_id": {
        "type": "keyword"
      },
      "chunk_index": {
        "type": "integer"
      },
      "chunk_content": {
        "type": "text",
        "analyzer": "text_analyzer"
      },
      "embedding": {
        "type": "dense_vector",
        "dims": 1536,
        "index": true,
        "similarity": "cosine"
      },
      "create_time": {
        "type": "date"
      },
      "update_time": {
        "type": "date"
      }
    }
  }'
);

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    order_id VARCHAR(64) NOT NULL UNIQUE,
    user_id INT NOT NULL,
    membership_type_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    duration INT NOT NULL, -- 购买时长（月）
    status SMALLINT NOT NULL DEFAULT 0, -- 0: 未支付, 1: 已支付, 2: 已取消
    payment_method VARCHAR(20), -- 支付方式
    payment_time TIMESTAMP,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (membership_type_id) REFERENCES membership_types(id)
);

-- 支付记录表
CREATE TABLE IF NOT EXISTS payment_records (
    id SERIAL PRIMARY KEY,
    order_id VARCHAR(64) NOT NULL,
    transaction_id VARCHAR(128), -- 第三方支付交易ID
    amount DECIMAL(10, 2) NOT NULL,
    status SMALLINT NOT NULL, -- 0: 处理中, 1: 成功, 2: 失败
    payment_method VARCHAR(20) NOT NULL,
    payment_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);

-- Casbin规则表（用于权限控制）
CREATE TABLE IF NOT EXISTS casbin_rule (
    id SERIAL PRIMARY KEY,
    ptype VARCHAR(10) NOT NULL,
    v0 VARCHAR(256),
    v1 VARCHAR(256),
    v2 VARCHAR(256),
    v3 VARCHAR(256),
    v4 VARCHAR(256),
    v5 VARCHAR(256)
);

-- 创建索引
CREATE INDEX idx_casbin_rule_ptype ON casbin_rule(ptype);
CREATE INDEX idx_casbin_rule_v0 ON casbin_rule(v0);
CREATE INDEX idx_casbin_rule_v1 ON casbin_rule(v1);

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(64),
    details TEXT,
    ip_address VARCHAR(50),
    user_agent TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 初始化管理员用户
INSERT INTO users (user_id, username, password, nickname, membership_type_id, status)
VALUES ('admin_001', 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '管理员', 
        (SELECT id FROM membership_types WHERE name = '超级会员'), 1)
ON CONFLICT (username) DO NOTHING;

-- 初始化默认分类
INSERT INTO categories (name, user_id)
VALUES ('默认分类', 1)
ON CONFLICT DO NOTHING;

-- 初始化Casbin规则
-- 格式: ptype, sub, obj, act
-- p: 策略, g: 角色继承关系
-- sub: 主体(用户或角色), obj: 资源, act: 操作

-- 角色继承关系
INSERT INTO casbin_rule (ptype, v0, v1) VALUES
('g', '月度会员', '普通用户'),
('g', '季度会员', '月度会员'),
('g', '年度会员', '季度会员'),
('g', '超级会员', '年度会员'),
('g', '机构会员', '超级会员');

-- 普通用户权限
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '普通用户', 'files', 'read'),
('p', '普通用户', 'files', 'create'),
('p', '普通用户', 'knowledge', 'read'),
('p', '普通用户', 'knowledge', 'create');

-- 月度会员额外权限
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '月度会员', 'files', 'share'),
('p', '月度会员', 'knowledge', 'share');

-- 季度会员额外权限
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '季度会员', 'files', 'version'),
('p', '季度会员', 'knowledge', 'export');

-- 年度会员额外权限
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '年度会员', 'api', 'access'),
('p', '年度会员', 'batch', 'process');

-- 超级会员额外权限
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '超级会员', 'premium', 'access'),
('p', '超级会员', 'priority', 'support');

-- 机构会员额外权限
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '机构会员', 'users', 'manage'),
('p', '机构会员', 'team', 'collaborate');
