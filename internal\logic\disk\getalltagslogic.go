package disk

import (
	"context"
	"errors"
	"fmt"
	"paper-editor-api/internal/middleware"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAllTagsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取所有标签
func NewGetAllTagsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAllTagsLogic {
	return &GetAllTagsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetAllTags 获取用户所有标签列表
// 该函数从上下文中获取当前用户ID，查询该用户创建的所有标签，
// 并统计每个标签关联的文件数量，最终返回标签列表响应
// 返回值:
//   - *types.TagListResp: 标签列表响应结构体，包含标签ID、名称、颜色和文件数量
//   - error: 查询过程中可能出现的错误信息
func (l *GetAllTagsLogic) GetAllTags() (*types.TagListResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 查询用户所有标签
	tags, err := l.svcCtx.FileTagsModel.FindByUserId(l.ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("查询标签失败: %w", err)
	}

	// 转换为响应格式
	var tagInfos []types.TagInfo
	for _, tag := range tags {
		// 统计该标签下的文件数量
		relations, err := l.svcCtx.FileTagRelationsModel.FindByTagId(l.ctx, tag.Id)
		if err != nil {
			logx.Errorf("统计标签文件数量失败: %v", err)
		}

		tagInfo := types.TagInfo{
			TagId:     tag.Id,
			TagName:   tag.TagName,
			Color:     tag.Color,
			FileCount: int64(len(relations)),
		}
		tagInfos = append(tagInfos, tagInfo)
	}

	return &types.TagListResp{
		Tags: tagInfos,
	}, nil
}
