package config

import (
	"github.com/zeromicro/go-zero/rest"
)

type Config struct {
	rest.RestConf
	Auth struct {
		AccessSecret string
		AccessExpire int64
	}
	Database struct {
		Driver string
		Source string
	}
	Cache []struct {
		Host string
		Pass string
		Type string
	}
	FileStorage struct {
		Type      string
		LocalPath string
		Endpoint  string
		AccessKey string
		SecretKey string
		Bucket    string
	}
	Elasticsearch struct {
		Addresses []string
		Username  string
		Password  string
		Index     string
	}
}
