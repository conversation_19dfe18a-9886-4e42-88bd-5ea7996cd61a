// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type AddFileTagReq struct {
	FileId string `json:"file_id"`
	TagId  int64  `json:"tag_id"`
}

type AddKnowledgeReq struct {
	Title      string   `json:"title"`
	Content    string   `json:"content"`
	Tags       []string `json:"tags,optional"`
	CategoryId int64    `json:"category_id,optional"`
}

type AddKnowledgeResp struct {
	KnowledgeId string `json:"knowledge_id"`
}

type BaseResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type ChangePasswordReq struct {
	OldPassword string `json:"old_password"`
	NewPassword string `json:"new_password"`
}

type CreateFolderReq struct {
	FolderName string `json:"folder_name"`
	ParentId   string `json:"parent_id,optional"`
}

type CreateFolderResp struct {
	FolderId string `json:"folder_id"`
}

type CreateTagReq struct {
	TagName string `json:"tag_name"`
	Color   string `json:"color,optional"`
}

type CreateTagResp struct {
	TagId int64 `json:"tag_id"`
}

type CreateTeamReq struct {
	TeamName    string `json:"team_name"`
	Description string `json:"description,optional"`
}

type CreateTeamResp struct {
	TeamId string `json:"team_id"`
}

type FileDetailResp struct {
	FileInfo FileInfo `json:"file_info"`
	Url      string   `json:"url"`
	Expires  int64    `json:"expires"`
}

type FileDownloadResp struct {
	Url string `json:"url"`
}

type FileInfo struct {
	FileId     string   `json:"file_id"`
	FileName   string   `json:"file_name"`
	FileSize   int64    `json:"file_size"`
	FileType   string   `json:"file_type"`
	UploadTime string   `json:"upload_time"`
	UpdateTime string   `json:"update_time"`
	FolderId   string   `json:"folder_id"`
	IsStarred  bool     `json:"is_starred"`
	Tags       []string `json:"tags"`
	Status     int      `json:"status"` // 1:正常 2:回收站 3:已删除
}

type FileListReq struct {
	FolderId string `form:"folder_id,optional"`
	TagName  string `form:"tag_name,optional"`
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
}

type FileListResp struct {
	Total       int64        `json:"total"`
	Files       []FileInfo   `json:"files"`
	Folders     []FolderInfo `json:"folders"`
	FileCount   int64        `json:"file_count"`
	FolderCount int64        `json:"folder_count"`
}

type FolderInfo struct {
	FolderId   string `json:"folder_id"`
	FolderName string `json:"folder_name"`
	ParentId   string `json:"parent_id"`
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
	Status     string `json:"status"`
}

type InviteTeamMemberReq struct {
	TeamId   string `json:"team_id"`
	Username string `json:"username"`
	Role     string `json:"role,default=member"`
}

type KnowledgeDetailResp struct {
	KnowledgeId string   `json:"knowledge_id"`
	Title       string   `json:"title"`
	Content     string   `json:"content"`
	Tags        []string `json:"tags"`
	CategoryId  int64    `json:"category_id"`
	Category    string   `json:"category"`
	CreateTime  string   `json:"create_time"`
	UpdateTime  string   `json:"update_time"`
}

type KnowledgeInfo struct {
	KnowledgeId    string   `json:"knowledge_id"`
	Title          string   `json:"title"`
	ContentPreview string   `json:"content_preview"`
	Tags           []string `json:"tags"`
	Category       string   `json:"category"`
	CreateTime     string   `json:"create_time"`
	UpdateTime     string   `json:"update_time"`
}

type LoginReq struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginResp struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	Expires      int64  `json:"expires"`
}

type MoveFileReq struct {
	FileId   string `json:"file_id"`
	FolderId string `json:"folder_id"`
}

type RefreshTokenReq struct {
	RefreshToken string `json:"refresh_token"`
}

type RefreshTokenResp struct {
	AccessToken string `json:"access_token"`
	Expires     int64  `json:"expires"`
}

type RegisterReq struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Email    string `json:"email,optional"`
	Nickname string `json:"nickname,optional"`
}

type RegisterResp struct {
	UserId string `json:"user_id"`
}

type RenameFileReq struct {
	FileId   string `json:"file_id"`
	FileName string `json:"file_name"`
}

type ResetPasswordReq struct {
	Email string `json:"email"`
}

type ResetPasswordResp struct {
	Message string `json:"message"`
}

type SearchKnowledgeReq struct {
	Keyword    string   `form:"keyword,optional"`
	Tags       []string `form:"tags,optional"`
	CategoryId int64    `form:"category_id,optional"`
	Page       int      `form:"page,default=1"`
	PageSize   int      `form:"page_size,default=20"`
}

type SearchKnowledgeResp struct {
	Total int64           `json:"total"`
	Items []KnowledgeInfo `json:"items"`
}

type ShareFileReq struct {
	FileId     string `json:"file_id"`
	ExpireDays int    `json:"expire_days,optional"` // 过期天数，0表示永不过期
	Password   string `json:"password,optional"`    // 分享密码
}

type ShareFileResp struct {
	ShareId    string `json:"share_id"`
	ShareUrl   string `json:"share_url"`
	Password   string `json:"password,optional"`
	ExpireTime string `json:"expire_time,optional"`
}

type TagInfo struct {
	TagId     int64  `json:"tag_id"`
	TagName   string `json:"tag_name"`
	Color     string `json:"color"`
	FileCount int64  `json:"file_count"`
}

type TagListResp struct {
	Tags []TagInfo `json:"tags"`
}

type TeamFileListReq struct {
	TeamId   string `form:"team_id"`
	FolderId string `form:"folder_id,optional"`
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
}

type TeamInfo struct {
	TeamId      string `json:"team_id"`
	TeamName    string `json:"team_name"`
	Description string `json:"description"`
	OwnerId     int64  `json:"owner_id"`
	MemberCount int    `json:"member_count"`
	CreateTime  string `json:"create_time"`
}

type TeamMemberInfo struct {
	UserId   int64  `json:"user_id"`
	Username string `json:"username"`
	Nickname string `json:"nickname"`
	Role     string `json:"role"` // owner, admin, member
	JoinTime string `json:"join_time"`
}

type UpdateUserInfoReq struct {
	Nickname string `json:"nickname,optional"`
	Email    string `json:"email,optional"`
	Phone    string `json:"phone,optional"`
}

type UploadAvatarResp struct {
	AvatarUrl string `json:"avatar_url"`
}

type UserInfoResp struct {
	UserId         string `json:"user_id"`
	Username       string `json:"username"`
	Nickname       string `json:"nickname"`
	Avatar         string `json:"avatar"`
	Email          string `json:"email"`
	Phone          string `json:"phone"`
	MembershipType string `json:"membership_type"`
	StorageQuota   int64  `json:"storage_quota"`
	KnowledgeQuota int64  `json:"knowledge_quota"`
	CreateTime     string `json:"create_time"`
}

type VerifyResetPasswordReq struct {
	Token       string `json:"token"`
	NewPassword string `json:"new_password"`
}
