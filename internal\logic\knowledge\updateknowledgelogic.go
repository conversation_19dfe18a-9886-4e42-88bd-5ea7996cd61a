package knowledge

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateKnowledgeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新知识点
func NewUpdateKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateKnowledgeLogic {
	return &UpdateKnowledgeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateKnowledgeLogic) UpdateKnowledge(req *types.AddKnowledgeReq) (resp *types.BaseResp, err error) {
	// todo: add your logic here and delete this line

	return
}
