package svc

import (
	"database/sql"
	"net/http"
	"paper-editor-api/internal/config"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/internal/storage"
	"paper-editor-api/model"

	_ "github.com/lib/pq"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type ServiceContext struct {
	Config               config.Config
	DB                   *sql.DB
	Storage              storage.Storage
	Auth                 func(next http.HandlerFunc) http.HandlerFunc
	FilesModel           model.FilesModel
	KnowledgeModel       model.KnowledgeModel
	UsersModel           model.UsersModel
	MembershipTypesModel model.MembershipTypesModel
	// 新增的模型
	FileTagsModel         model.FileTagsModel
	FileTagRelationsModel model.FileTagRelationsModel
	FileAccessLogsModel   model.FileAccessLogsModel

	StarredFilesModel model.StarredFilesModel
	TeamsModel        model.TeamsModel
	TeamMembersModel  model.TeamMembersModel
	FoldersModel      model.FoldersModel
	FileVersionsModel model.FileVersionsModel
	FileSharesModel   model.FileSharesModel
	TagsModel         model.TagsModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	// 连接数据库
	db, err := sql.Open(c.Database.Driver, c.Database.Source)
	if err != nil {
		panic(err)
	}

	// 测试数据库连接
	if err := db.Ping(); err != nil {
		panic(err)
	}

	// 创建sqlx连接
	conn := sqlx.NewSqlConn(c.Database.Driver, c.Database.Source)

	authMiddleware := middleware.NewAuthMiddleware(c)

	return &ServiceContext{
		Config:                c,
		DB:                    db,
		Storage:               storage.NewStorage(c),
		Auth:                  authMiddleware.Handle,
		FilesModel:            model.NewFilesModel(conn),
		KnowledgeModel:        model.NewKnowledgeModel(conn),
		UsersModel:            model.NewUsersModel(conn),
		MembershipTypesModel:  model.NewMembershipTypesModel(conn),
		FileAccessLogsModel:   model.NewFileAccessLogsModel(conn),
		FileTagsModel:         model.NewFileTagsModel(conn),
		FileTagRelationsModel: model.NewFileTagRelationsModel(conn),
		StarredFilesModel:     model.NewStarredFilesModel(conn),
		TeamsModel:            model.NewTeamsModel(conn),
		TeamMembersModel:      model.NewTeamMembersModel(conn),
		FoldersModel:          model.NewFoldersModel(conn),
		FileVersionsModel:     model.NewFileVersionsModel(conn),
		FileSharesModel:       model.NewFileSharesModel(conn),
		TagsModel:             model.NewTagsModel(conn),
	}
}
