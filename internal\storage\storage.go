package storage

import (
	"fmt"
	"io"
	"mime/multipart"
	"paper-editor-api/internal/config"
	"path/filepath"
)

// 存储接口
type Storage interface {
	// 保存文件
	SaveFile(file *multipart.FileHeader, path string) (string, error)

	// 获取文件
	GetFile(path string) (io.ReadCloser, error)

	// 删除文件
	DeleteFile(path string) error

	// 生成访问URL
	GenerateURL(path string, expire int64) (string, error)
}

// 存储工厂
func NewStorage(c config.Config) Storage {
	switch c.FileStorage.Type {
	case "local":
		return &LocalStorage{basePath: c.FileStorage.LocalPath}
	case "minio":
		// 创建MinIO存储实例
		minioStorage, err := NewMinioStorage(
			c.FileStorage.Endpoint,
			c.FileStorage.AccessKey,
			c.FileStorage.SecretKey,
			c.FileStorage.Bucket,
		)
		if err != nil {
			// 如果MinIO初始化失败，记录错误并回退到本地存储
			fmt.Printf("初始化MinIO存储失败: %v, 回退到本地存储\n", err)
			return &LocalStorage{basePath: c.FileStorage.LocalPath}
		}
		return minioStorage
	default:
		// 默认使用本地存储
		return &LocalStorage{basePath: c.FileStorage.LocalPath}
	}
}

// 生成存储路径
func GenerateStoragePath(userId int64, fileName string) string {
	userIdStr := fmt.Sprintf("%d", userId)
	return filepath.Join("files", "user", userIdStr, fileName)
}
