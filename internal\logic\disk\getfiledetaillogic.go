package disk

import (
	"context"
	"errors"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
	"paper-editor-api/model"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFileDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取文件详情
func NewGetFileDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFileDetailLogic {
	return &GetFileDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetFileDetail 获取文件的详细信息，包括文件元数据、访问URL、标星状态和标签等。
// 该方法从上下文中提取文件ID和用户ID，验证权限后查询文件信息，并生成临时访问链接。
//
// 参数:
//   - 无显式参数，通过逻辑上下文 l.ctx 和服务上下文 l.svcCtx 获取所需数据
//
// 返回值:
//   - resp (*types.FileDetailResp): 包含文件详细信息及访问链接的响应结构体
//   - err (error): 如果操作过程中发生错误，则返回相应的错误信息
func (l *GetFileDetailLogic) GetFileDetail() (resp *types.FileDetailResp, err error) {
	// 从上下文中获取文件ID
	fileIdStr, ok := l.ctx.Value("fileId").(string)
	if !ok || fileIdStr == "" {
		return nil, errors.New("无效的文件ID")
	}

	// 转换文件ID为int64
	fileId, err := strconv.ParseInt(fileIdStr, 10, 64)
	if err != nil {
		return nil, errors.New("无效的文件ID格式")
	}

	// 查询文件元数据
	file, err := l.svcCtx.FilesModel.FindOne(l.ctx, fileId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, errors.New("文件不存在")
		}
		logx.Errorf("查询文件失败: %v", err)
		return nil, errors.New("获取文件信息失败")
	}

	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 检查文件是否属于当前用户
	if file.UserId != userId {
		return nil, errors.New("无权限查看此文件")
	}

	// 使用存储服务生成文件访问URL
	// 设置URL过期时间为1小时
	expireDuration := int64(3600) // 1小时 = 3600秒
	fileURL, err := l.svcCtx.Storage.GenerateURL(file.Path, expireDuration)
	if err != nil {
		logx.Errorf("生成文件访问URL失败: %v", err)
		return nil, errors.New("生成文件访问链接失败")
	}

	// 检查文件是否被标星
	isStarred := false
	starredFile, err := l.svcCtx.StarredFilesModel.FindByFileIdAndUserId(l.ctx, file.FileId, userId)
	if err != nil {
		if err != model.ErrNotFound {
			logx.Errorf("查询文件标星状态失败: %v", err)
		}
		// 查询标星状态失败不影响整体功能，继续执行
	} else if starredFile != nil {
		isStarred = true
	}

	// 获取文件标签
	var tags []string
	fileTags, err := l.svcCtx.FileTagRelationsModel.FindTagsByFileId(l.ctx, file.FileId)
	if err != nil {
		logx.Errorf("获取文件标签失败: %v", err)
		// 获取标签失败不影响整体功能，继续执行
	} else if len(fileTags) > 0 {
		for _, tag := range fileTags {
			tags = append(tags, tag.TagName)
		}
	}

	// 构建文件信息响应
	fileInfo := types.FileInfo{
		FileId:     file.FileId,
		FileName:   file.FileName,
		FileSize:   file.FileSize,
		FileType:   file.FileType,
		UploadTime: file.CreateTime.Format(time.DateTime),
		UpdateTime: file.UpdateTime.Format(time.DateTime),
		FolderId:   file.FolderId.String,
		IsStarred:  isStarred,
		Tags:       tags,
		Status:     int(file.Status),
	}

	// 记录文件访问日志
	_, err = l.svcCtx.FileAccessLogsModel.Insert(l.ctx, &model.FileAccessLogs{
		FileId:     file.FileId,
		UserId:     userId,
		AccessTime: time.Now(),
		AccessType: 1, // 访问类型：1-查看 2-下载 3-编辑
	})

	return &types.FileDetailResp{
		FileInfo: fileInfo,
		Url:      fileURL,
		Expires:  expireDuration,
	}, nil
}
