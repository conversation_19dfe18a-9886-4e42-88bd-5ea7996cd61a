package disk

import (
	"context"
	"errors"
	"fmt"
	"paper-editor-api/internal/middleware"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetStarredFilesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取已标星文件列表
func NewGetStarredFilesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetStarredFilesLogic {
	return &GetStarredFilesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetStarredFiles 获取用户标星的文件列表
func (l *GetStarredFilesLogic) GetStarredFiles(req *types.FileListReq) (*types.FileListResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 查询用户标星的文件
	starredFiles, err := l.svcCtx.StarredFilesModel.FindByUserId(l.ctx, userId, req.Page, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("查询标星文件失败: %w", err)
	}

	// 统计总数
	total, err := l.svcCtx.StarredFilesModel.CountByUserId(l.ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("统计标星文件数量失败: %w", err)
	}

	// 获取文件详细信息
	var fileInfos []types.FileInfo
	for _, starredFile := range starredFiles {
		file, err := l.svcCtx.FilesModel.FindOneByFileId(l.ctx, starredFile.FileId)
		if err != nil {
			logx.Errorf("查询文件信息失败: %v", err)
			continue
		}

		fileInfo := types.FileInfo{
			FileId:     file.FileId,
			FileName:   file.FileName,
			FileSize:   file.FileSize,
			FileType:   file.FileType,
			UploadTime: file.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime: file.UpdateTime.Format("2006-01-02 15:04:05"),
			FolderId:   file.FolderId.String,
		}
		fileInfos = append(fileInfos, fileInfo)
	}

	return &types.FileListResp{
		Total:   total,
		Files:   fileInfos,
		Folders: []types.FolderInfo{}, // 标星文件列表不包含文件夹
	}, nil
}
