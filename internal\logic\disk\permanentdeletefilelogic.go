package disk

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PermanentDeleteFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 彻底删除文件
func NewPermanentDeleteFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PermanentDeleteFileLogic {
	return &PermanentDeleteFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PermanentDeleteFileLogic) PermanentDeleteFile() (resp *types.BaseResp, err error) {
	// todo: add your logic here and delete this line

	return
}
