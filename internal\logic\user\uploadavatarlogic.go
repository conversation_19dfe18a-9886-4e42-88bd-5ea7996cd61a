package user

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"mime/multipart"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/internal/storage"
	"path/filepath"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UploadAvatarLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 上传头像
func NewUploadAvatarLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadAvatarLogic {
	return &UploadAvatarLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UploadAvatarLogic) UploadAvatar(file *multipart.FileHeader) (*types.UploadAvatarResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 检查文件类型
	ext := filepath.Ext(file.Filename)
	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" && ext != ".gif" {
		return nil, errors.New("不支持的文件类型，仅支持 jpg、jpeg、png、gif")
	}

	// 检查文件大小 (限制为5MB)
	if file.Size > 5*1024*1024 {
		return nil, errors.New("文件大小不能超过5MB")
	}

	// 生成存储路径
	avatarPath := storage.GenerateStoragePath(userId, fmt.Sprintf("avatar_%d%s", userId, ext))

	// 创建存储实例
	fileStorage := storage.NewStorage(l.svcCtx.Config)

	// 保存文件
	path, err := fileStorage.SaveFile(file, avatarPath)
	if err != nil {
		return nil, fmt.Errorf("保存头像失败: %w", err)
	}

	// 更新用户头像路径
	user, err := l.svcCtx.UsersModel.FindOne(context.Background(), userId)
	if err != nil {
		// 如果数据库更新失败，删除已上传的文件
		_ = fileStorage.DeleteFile(path)
		return nil, errors.New("用户不存在")
	}

	// 删除旧头像文件
	if user.Avatar.Valid && user.Avatar.String != "" {
		_ = fileStorage.DeleteFile(user.Avatar.String)
	}

	// 更新用户头像
	user.Avatar = sql.NullString{String: path, Valid: true}
	err = l.svcCtx.UsersModel.Update(context.Background(), user)
	if err != nil {
		// 如果数据库更新失败，删除已上传的文件
		_ = fileStorage.DeleteFile(path)
		return nil, errors.New("头像更新失败")
	}

	// 生成访问URL
	avatarUrl, err := fileStorage.GenerateURL(path, 0)
	if err != nil {
		avatarUrl = "/files/" + path // 降级处理
	}

	return &types.UploadAvatarResp{
		AvatarUrl: avatarUrl,
	}, nil
}
