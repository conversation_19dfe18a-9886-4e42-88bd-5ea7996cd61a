package user

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/user"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 重置密码请求
func ResetPasswordHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ResetPasswordReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := user.NewResetPasswordLogic(r.Context(), svcCtx)
		resp, err := l.ResetPassword(&req)
		response.Response(w, resp, err)
	}
}
