package disk

import (
	"context"
	"database/sql"
	"fmt"
	"paper-editor-api/model"
	"paper-editor-api/pkg/helper"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateFolderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建文件夹
func NewCreateFolderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateFolderLogic {
	return &CreateFolderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// CreateFolder 创建文件夹
// @param req *types.CreateFolderReq 请求参数，包含文件夹名称和父文件夹ID
// @return resp *types.CreateFolderResp 返回参数，包含创建的文件夹ID
// @return err error 错误信息，创建失败时返回具体错误
func (l *CreateFolderLogic) CreateFolder(req *types.CreateFolderReq) (resp *types.CreateFolderResp, err error) {
	resp = &types.CreateFolderResp{}

	// 生成新的文件夹ID
	folderId := helper.Utils.GenerateId(helper.GENERATE_XID)

	// 创建文件夹记录
	parentId := sql.NullString{String: req.ParentId, Valid: req.ParentId != ""}
	if !parentId.Valid {
		parentId.String = "0" // 设置默认值
		parentId.Valid = true
	}

	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return nil, fmt.Errorf("获取用户ID失败")
	}

	// 插入文件夹记录到数据库
	_, err = l.svcCtx.FoldersModel.Insert(l.ctx, &model.Folders{
		FolderName: req.FolderName,
		ParentId:   parentId,
		FolderId:   folderId,
		UserId:     userId,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
		Status:     1, // 根据数据库设计，Status 是 int64 类型
	})
	if err != nil {
		return nil, fmt.Errorf("创建文件夹失败: %v", err)
	}

	resp.FolderId = folderId
	return
}
