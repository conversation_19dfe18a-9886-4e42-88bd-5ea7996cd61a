// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	casbinRuleFieldNames          = builder.RawFieldNames(&CasbinRule{}, true)
	casbinRuleRows                = strings.Join(casbinRuleFieldNames, ",")
	casbinRuleRowsExpectAutoSet   = strings.Join(stringx.Remove(casbinRuleFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	casbinRuleRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(casbinRuleFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	casbinRuleModel interface {
		Insert(ctx context.Context, data *CasbinRule) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*CasbinRule, error)
		Update(ctx context.Context, data *CasbinRule) error
		Delete(ctx context.Context, id int64) error
	}

	defaultCasbinRuleModel struct {
		conn  sqlx.SqlConn
		table string
	}

	CasbinRule struct {
		Id    int64          `db:"id"`
		Ptype string         `db:"ptype"`
		V0    sql.NullString `db:"v0"`
		V1    sql.NullString `db:"v1"`
		V2    sql.NullString `db:"v2"`
		V3    sql.NullString `db:"v3"`
		V4    sql.NullString `db:"v4"`
		V5    sql.NullString `db:"v5"`
	}
)

func newCasbinRuleModel(conn sqlx.SqlConn) *defaultCasbinRuleModel {
	return &defaultCasbinRuleModel{
		conn:  conn,
		table: `"public"."casbin_rule"`,
	}
}

func (m *defaultCasbinRuleModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultCasbinRuleModel) FindOne(ctx context.Context, id int64) (*CasbinRule, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", casbinRuleRows, m.table)
	var resp CasbinRule
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultCasbinRuleModel) Insert(ctx context.Context, data *CasbinRule) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6, $7)", m.table, casbinRuleRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Ptype, data.V0, data.V1, data.V2, data.V3, data.V4, data.V5)
	return ret, err
}

func (m *defaultCasbinRuleModel) Update(ctx context.Context, data *CasbinRule) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, casbinRuleRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Id, data.Ptype, data.V0, data.V1, data.V2, data.V3, data.V4, data.V5)
	return err
}

func (m *defaultCasbinRuleModel) tableName() string {
	return m.table
}
