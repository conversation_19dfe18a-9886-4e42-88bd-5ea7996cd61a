// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	usersFieldNames          = builder.RawFieldNames(&Users{}, true)
	usersRows                = strings.Join(usersFieldNames, ",")
	usersRowsExpectAutoSet   = strings.Join(stringx.Remove(usersFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	usersRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(usersFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	usersModel interface {
		Insert(ctx context.Context, data *Users) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Users, error)
		FindOneByUserId(ctx context.Context, userId string) (*Users, error)
		FindOneByUsername(ctx context.Context, username string) (*Users, error)
		Update(ctx context.Context, data *Users) error
		Delete(ctx context.Context, id int64) error
	}

	defaultUsersModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Users struct {
		Id                   int64          `db:"id"`
		UserId               string         `db:"user_id"`
		Username             string         `db:"username"`
		Password             string         `db:"password"`
		Nickname             sql.NullString `db:"nickname"`
		Avatar               sql.NullString `db:"avatar"`
		Email                sql.NullString `db:"email"`
		Phone                sql.NullString `db:"phone"`
		MembershipTypeId     int64          `db:"membership_type_id"`
		MembershipExpireTime sql.NullTime   `db:"membership_expire_time"`
		StorageQuota         int64          `db:"storage_quota"`
		KnowledgeQuota       int64          `db:"knowledge_quota"`
		ParentUserId         sql.NullInt64  `db:"parent_user_id"`
		Status               int64          `db:"status"`
		CreateTime           time.Time      `db:"create_time"`
		UpdateTime           time.Time      `db:"update_time"`
	}
)

func newUsersModel(conn sqlx.SqlConn) *defaultUsersModel {
	return &defaultUsersModel{
		conn:  conn,
		table: `"public"."users"`,
	}
}

func (m *defaultUsersModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUsersModel) FindOne(ctx context.Context, id int64) (*Users, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", usersRows, m.table)
	var resp Users
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUsersModel) FindOneByUserId(ctx context.Context, userId string) (*Users, error) {
	var resp Users
	query := fmt.Sprintf("select %s from %s where user_id = $1 limit 1", usersRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, userId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUsersModel) FindOneByUsername(ctx context.Context, username string) (*Users, error) {
	var resp Users
	query := fmt.Sprintf("select %s from %s where username = $1 limit 1", usersRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, username)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUsersModel) Insert(ctx context.Context, data *Users) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)", m.table, usersRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.UserId, data.Username, data.Password, data.Nickname, data.Avatar, data.Email, data.Phone, data.MembershipTypeId, data.MembershipExpireTime, data.StorageQuota, data.KnowledgeQuota, data.ParentUserId, data.Status)
	return ret, err
}

func (m *defaultUsersModel) Update(ctx context.Context, newData *Users) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, usersRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.UserId, newData.Username, newData.Password, newData.Nickname, newData.Avatar, newData.Email, newData.Phone, newData.MembershipTypeId, newData.MembershipExpireTime, newData.StorageQuota, newData.KnowledgeQuota, newData.ParentUserId, newData.Status)
	return err
}

func (m *defaultUsersModel) tableName() string {
	return m.table
}
