// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	knowledgeTagFieldNames          = builder.RawFieldNames(&KnowledgeTag{}, true)
	knowledgeTagRows                = strings.Join(knowledgeTagFieldNames, ",")
	knowledgeTagRowsExpectAutoSet   = strings.Join(stringx.Remove(knowledgeTagFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	knowledgeTagRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(knowledgeTagFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	knowledgeTagModel interface {
		Insert(ctx context.Context, data *KnowledgeTag) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*KnowledgeTag, error)
		FindOneByKnowledgeIdTagId(ctx context.Context, knowledgeId string, tagId int64) (*KnowledgeTag, error)
		Update(ctx context.Context, data *KnowledgeTag) error
		Delete(ctx context.Context, id int64) error
	}

	defaultKnowledgeTagModel struct {
		conn  sqlx.SqlConn
		table string
	}

	KnowledgeTag struct {
		Id          int64  `db:"id"`
		KnowledgeId string `db:"knowledge_id"`
		TagId       int64  `db:"tag_id"`
	}
)

func newKnowledgeTagModel(conn sqlx.SqlConn) *defaultKnowledgeTagModel {
	return &defaultKnowledgeTagModel{
		conn:  conn,
		table: `"public"."knowledge_tag"`,
	}
}

func (m *defaultKnowledgeTagModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultKnowledgeTagModel) FindOne(ctx context.Context, id int64) (*KnowledgeTag, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", knowledgeTagRows, m.table)
	var resp KnowledgeTag
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultKnowledgeTagModel) FindOneByKnowledgeIdTagId(ctx context.Context, knowledgeId string, tagId int64) (*KnowledgeTag, error) {
	var resp KnowledgeTag
	query := fmt.Sprintf("select %s from %s where knowledge_id = $1 and tag_id = $2 limit 1", knowledgeTagRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, knowledgeId, tagId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultKnowledgeTagModel) Insert(ctx context.Context, data *KnowledgeTag) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2)", m.table, knowledgeTagRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.KnowledgeId, data.TagId)
	return ret, err
}

func (m *defaultKnowledgeTagModel) Update(ctx context.Context, newData *KnowledgeTag) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, knowledgeTagRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.KnowledgeId, newData.TagId)
	return err
}

func (m *defaultKnowledgeTagModel) tableName() string {
	return m.table
}
