package team

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RemoveTeamMemberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 移除团队成员
func NewRemoveTeamMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveTeamMemberLogic {
	return &RemoveTeamMemberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RemoveTeamMemberLogic) RemoveTeamMember() (resp *types.BaseResp, err error) {
	// todo: add your logic here and delete this line

	return
}
