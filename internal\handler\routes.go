// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package handler

import (
	"net/http"

	disk "paper-editor-api/internal/handler/disk"
	knowledge "paper-editor-api/internal/handler/knowledge"
	team "paper-editor-api/internal/handler/team"
	user "paper-editor-api/internal/handler/user"
	"paper-editor-api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					// 下载文件
					Method:  http.MethodGet,
					Path:    "/download/:fileId",
					Handler: disk.DownloadFileHandler(serverCtx),
				},
				{
					// 删除文件
					Method:  http.MethodDelete,
					Path:    "/file/:fileId",
					Handler: disk.DeleteFileHandler(serverCtx),
				},
				{
					// 获取文件详情
					Method:  http.MethodGet,
					Path:    "/file/:fileId",
					Handler: disk.GetFileDetailHandler(serverCtx),
				},
				{
					// 移除文件标签
					Method:  http.MethodDelete,
					Path:    "/file/:fileId/tag/:tagId",
					Handler: disk.RemoveFileTagHandler(serverCtx),
				},
				{
					// 为文件添加标签
					Method:  http.MethodPost,
					Path:    "/file/tag",
					Handler: disk.AddFileTagHandler(serverCtx),
				},
				{
					// 获取文件列表
					Method:  http.MethodGet,
					Path:    "/files",
					Handler: disk.GetFileListHandler(serverCtx),
				},
				{
					// 创建文件夹
					Method:  http.MethodPost,
					Path:    "/folder",
					Handler: disk.CreateFolderHandler(serverCtx),
				},
				{
					// 移动文件
					Method:  http.MethodPut,
					Path:    "/move",
					Handler: disk.MoveFileHandler(serverCtx),
				},
				{
					// 获取最近访问文件列表
					Method:  http.MethodGet,
					Path:    "/recent",
					Handler: disk.GetRecentFilesHandler(serverCtx),
				},
				{
					// 重命名文件
					Method:  http.MethodPut,
					Path:    "/rename",
					Handler: disk.RenameFileHandler(serverCtx),
				},
				{
					// 分享文件
					Method:  http.MethodPost,
					Path:    "/share",
					Handler: disk.ShareFileHandler(serverCtx),
				},
				{
					// 获取共享文件列表
					Method:  http.MethodGet,
					Path:    "/shared",
					Handler: disk.GetSharedFilesHandler(serverCtx),
				},
				{
					// 标星文件
					Method:  http.MethodPut,
					Path:    "/star/:fileId",
					Handler: disk.StarFileHandler(serverCtx),
				},
				{
					// 取消标星文件
					Method:  http.MethodDelete,
					Path:    "/star/:fileId",
					Handler: disk.UnstarFileHandler(serverCtx),
				},
				{
					// 获取已标星文件列表
					Method:  http.MethodGet,
					Path:    "/starred",
					Handler: disk.GetStarredFilesHandler(serverCtx),
				},
				{
					// 根据标签获取文件列表
					Method:  http.MethodGet,
					Path:    "/tag/:tagId/files",
					Handler: disk.GetFilesByTagHandler(serverCtx),
				},
				{
					// 获取所有标签
					Method:  http.MethodGet,
					Path:    "/tags",
					Handler: disk.GetAllTagsHandler(serverCtx),
				},
				{
					// 创建标签
					Method:  http.MethodPost,
					Path:    "/tags",
					Handler: disk.CreateTagHandler(serverCtx),
				},
				{
					// 删除标签
					Method:  http.MethodDelete,
					Path:    "/tags/:tagId",
					Handler: disk.DeleteTagHandler(serverCtx),
				},
				{
					// 获取回收站文件列表
					Method:  http.MethodGet,
					Path:    "/trash",
					Handler: disk.GetTrashFilesHandler(serverCtx),
				},
				{
					// 彻底删除文件
					Method:  http.MethodDelete,
					Path:    "/trash/:fileId",
					Handler: disk.PermanentDeleteFileHandler(serverCtx),
				},
				{
					// 清空回收站
					Method:  http.MethodDelete,
					Path:    "/trash/empty",
					Handler: disk.EmptyTrashHandler(serverCtx),
				},
				{
					// 恢复回收站文件
					Method:  http.MethodPut,
					Path:    "/trash/restore/:fileId",
					Handler: disk.RestoreFileHandler(serverCtx),
				},
				{
					// 上传文件
					Method:  http.MethodPost,
					Path:    "/upload",
					Handler: disk.UploadFileHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/v1/disk"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					// 删除知识点
					Method:  http.MethodDelete,
					Path:    "/:knowledgeId",
					Handler: knowledge.DeleteKnowledgeHandler(serverCtx),
				},
				{
					// 更新知识点
					Method:  http.MethodPut,
					Path:    "/:knowledgeId",
					Handler: knowledge.UpdateKnowledgeHandler(serverCtx),
				},
				{
					// 添加知识点
					Method:  http.MethodPost,
					Path:    "/add",
					Handler: knowledge.AddKnowledgeHandler(serverCtx),
				},
				{
					// 获取知识点详情
					Method:  http.MethodGet,
					Path:    "/detail/:knowledgeId",
					Handler: knowledge.GetKnowledgeDetailHandler(serverCtx),
				},
				{
					// 搜索知识点
					Method:  http.MethodGet,
					Path:    "/search",
					Handler: knowledge.SearchKnowledgeHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/v1/knowledge"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					// 获取团队详情
					Method:  http.MethodGet,
					Path:    "/:teamId",
					Handler: team.GetTeamDetailHandler(serverCtx),
				},
				{
					// 获取团队文件列表
					Method:  http.MethodGet,
					Path:    "/:teamId/files",
					Handler: team.GetTeamFilesHandler(serverCtx),
				},
				{
					// 退出团队
					Method:  http.MethodDelete,
					Path:    "/:teamId/leave",
					Handler: team.LeaveTeamHandler(serverCtx),
				},
				{
					// 移除团队成员
					Method:  http.MethodDelete,
					Path:    "/:teamId/member/:userId",
					Handler: team.RemoveTeamMemberHandler(serverCtx),
				},
				{
					// 获取团队成员列表
					Method:  http.MethodGet,
					Path:    "/:teamId/members",
					Handler: team.GetTeamMembersHandler(serverCtx),
				},
				{
					// 上传团队文件
					Method:  http.MethodPost,
					Path:    "/:teamId/upload",
					Handler: team.UploadTeamFileHandler(serverCtx),
				},
				{
					// 创建团队
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: team.CreateTeamHandler(serverCtx),
				},
				{
					// 邀请团队成员
					Method:  http.MethodPost,
					Path:    "/invite",
					Handler: team.InviteTeamMemberHandler(serverCtx),
				},
				{
					// 获取我的团队列表
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: team.GetMyTeamsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/v1/team"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 用户登录
				Method:  http.MethodPost,
				Path:    "/login",
				Handler: user.LoginHandler(serverCtx),
			},
			{
				// 刷新令牌
				Method:  http.MethodPost,
				Path:    "/refresh",
				Handler: user.RefreshTokenHandler(serverCtx),
			},
			{
				// 用户注册
				Method:  http.MethodPost,
				Path:    "/register",
				Handler: user.RegisterHandler(serverCtx),
			},
			{
				// 重置密码请求
				Method:  http.MethodPost,
				Path:    "/reset-password",
				Handler: user.ResetPasswordHandler(serverCtx),
			},
			{
				// 验证重置密码
				Method:  http.MethodPost,
				Path:    "/verify-reset-password",
				Handler: user.VerifyResetPasswordHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/user"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					// 删除账户
					Method:  http.MethodDelete,
					Path:    "/account",
					Handler: user.DeleteAccountHandler(serverCtx),
				},
				{
					// 上传头像
					Method:  http.MethodPost,
					Path:    "/avatar",
					Handler: user.UploadAvatarHandler(serverCtx),
				},
				{
					// 获取用户信息
					Method:  http.MethodGet,
					Path:    "/info",
					Handler: user.GetUserInfoHandler(serverCtx),
				},
				{
					// 更新用户信息
					Method:  http.MethodPut,
					Path:    "/info",
					Handler: user.UpdateUserInfoHandler(serverCtx),
				},
				{
					// 用户退出登录
					Method:  http.MethodPost,
					Path:    "/logout",
					Handler: user.LogoutHandler(serverCtx),
				},
				{
					// 修改密码
					Method:  http.MethodPut,
					Path:    "/password",
					Handler: user.ChangePasswordHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/v1/user"),
	)
}
