package model

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ FoldersModel = (*customFoldersModel)(nil)

type (
	// FoldersModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFoldersModel.
	FoldersModel interface {
		foldersModel
		withSession(session sqlx.Session) FoldersModel
		FindTrashByUserId(ctx context.Context, userId int64, page, pageSize int) ([]*Folders, error)
		CountTrashByUserId(ctx context.Context, userId int64) (int64, error)
		FindAllByParentId(ctx context.Context, userId int64, parentId string, page, pageSize int) ([]*Folders, error)
		CountByParentId(ctx context.Context, userId int64, parentId string) (int64, error)
	}

	customFoldersModel struct {
		*defaultFoldersModel
	}
)

// NewFoldersModel returns a model for the database table.
func NewFoldersModel(conn sqlx.SqlConn) FoldersModel {
	return &customFoldersModel{
		defaultFoldersModel: newFoldersModel(conn),
	}
}

func (m *customFoldersModel) withSession(session sqlx.Session) FoldersModel {
	return NewFoldersModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customFoldersModel) FindTrashByUserId(ctx context.Context, userId int64, page, pageSize int) ([]*Folders, error) {
	// 查询已删除的文件夹（status = 2 表示已删除）
	// 按照更新时间降序排序，最近删除的排在前面
	query := fmt.Sprintf("select %s from %s where user_id = $1 and status = 2 order by update_time desc offset $2 limit $3", foldersRows, m.table)
	var folders []*Folders
	err := m.conn.QueryRowsCtx(ctx, &folders, query, userId, (page-1)*pageSize, pageSize)
	if err != nil {
		return nil, err
	}

	return folders, nil
}

func (m *customFoldersModel) CountTrashByUserId(ctx context.Context, userId int64) (int64, error) {
	// 统计已删除的文件夹数量
	query := fmt.Sprintf("select count(*) from %s where user_id = $1 and status = 2", m.table)
	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, userId)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m *customFoldersModel) FindAllByParentId(ctx context.Context, userId int64, parentId string, page, pageSize int) ([]*Folders, error) {
	offset := (page - 1) * pageSize
	query := fmt.Sprintf("select %s from %s where `user_id` = ? and `parent_id` = ? and `status` = 'active' order by `update_time` desc limit ?, ?", foldersRows, m.table)
	var resp []*Folders
	err := m.conn.QueryRowsCtx(ctx, &resp, query, userId, parentId, offset, pageSize)
	return resp, err
}

func (m *customFoldersModel) CountByParentId(ctx context.Context, userId int64, parentId string) (int64, error) {
	query := fmt.Sprintf("select count(*) from %s where `user_id` = ? and `parent_id` = ? and `status` = 'active'", m.table)
	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, userId, parentId)
	return count, err
}
