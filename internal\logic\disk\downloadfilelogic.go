package disk

import (
	"context"
	"errors"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
	"paper-editor-api/model"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
)

type DownloadFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 下载文件
func NewDownloadFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DownloadFileLogic {
	return &DownloadFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// DownloadFile 下载文件
// 参数:
//   - 无显式参数，从上下文中获取文件ID
//
// 返回值:
//   - *types.FileDownloadResp 包含文件下载URL和过期时间
//   - error 错误信息，如果操作失败则返回错误
func (l *DownloadFileLogic) DownloadFile() (resp *types.FileDownloadResp, err error) {
	// 从上下文中获取文件ID
	fileIdStr, ok := l.ctx.Value("fileId").(string)
	if !ok || fileIdStr == "" {
		return nil, errors.New("无效的文件ID")
	}

	// 转换文件ID为int64
	fileId, err := strconv.ParseInt(fileIdStr, 10, 64)
	if err != nil {
		return nil, errors.New("无效的文件ID格式")
	}

	// 查询文件元数据
	file, err := l.svcCtx.FilesModel.FindOne(l.ctx, fileId)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, errors.New("文件不存在")
		}
		logx.Errorf("查询文件失败: %v", err)
		return nil, errors.New("获取文件信息失败")
	}

	// 使用ServiceContext中的存储服务实例
	// 设置URL过期时间为1小时
	expireDuration := int64(3600) // 1小时 = 3600秒
	downloadURL, err := l.svcCtx.Storage.GenerateURL(file.Path, expireDuration)
	if err != nil {
		logx.Errorf("生成文件下载URL失败: %v", err)
		return nil, errors.New("生成文件下载链接失败")
	}

	// 计算过期时间
	//expiresAt := time.Now().Add(time.Duration(expireDuration) * time.Second).Unix()
	//
	//if err != nil {
	//	logx.Errorf("记录文件下载日志失败: %v", err)
	//}

	return &types.FileDownloadResp{
		Url: downloadURL,
	}, nil
}
