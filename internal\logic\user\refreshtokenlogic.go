package user

import (
	"context"
	"errors"
	"github.com/golang-jwt/jwt/v4"
	"time"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RefreshTokenLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 刷新令牌
func NewRefreshTokenLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RefreshTokenLogic {
	return &RefreshTokenLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RefreshTokenLogic) RefreshToken(req *types.RefreshTokenReq) (*types.RefreshTokenResp, error) {
	// 1. 验证刷新令牌
	token, err := jwt.Parse(req.RefreshToken, func(token *jwt.Token) (interface{}, error) {
		return []byte(l.svcCtx.Config.Auth.AccessSecret), nil
	})

	if err != nil {
		return nil, errors.New("无效的刷新令牌")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok || !token.Valid {
		return nil, errors.New("无效的刷新令牌")
	}

	// 2. 从令牌中获取用户信息
	userId, ok := claims["userId"].(float64)
	if !ok {
		return nil, errors.New("无效的刷新令牌")
	}

	username, ok := claims["username"].(string)
	if !ok {
		return nil, errors.New("无效的刷新令牌")
	}

	role, ok := claims["role"].(string)
	if !ok {
		return nil, errors.New("无效的刷新令牌")
	}

	// 3. 生成新的访问令牌
	now := time.Now()
	accessExpire := l.svcCtx.Config.Auth.AccessExpire
	accessToken, err := l.generateToken(int64(userId), username, role, now, accessExpire)
	if err != nil {
		return nil, err
	}

	return &types.RefreshTokenResp{
		AccessToken: accessToken,
		Expires:     now.Unix() + accessExpire,
	}, nil
}

// 生成JWT令牌
func (l *RefreshTokenLogic) generateToken(userId int64, username, role string, now time.Time, expire int64) (string, error) {
	claims := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"userId":   userId,
		"username": username,
		"role":     role,
		"exp":      now.Unix() + expire,
		"iat":      now.Unix(),
	})

	return claims.SignedString([]byte(l.svcCtx.Config.Auth.AccessSecret))
}
