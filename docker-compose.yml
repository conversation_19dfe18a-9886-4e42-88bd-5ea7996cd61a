# 使用最新的Docker Compose格式

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: paper-editor-postgres
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: paper_editor
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # 初始化数据库脚本
      - ./sql/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - paper-editor-network

  # Elasticsearch 搜索引擎
  elasticsearch:
    image: elasticsearch:8.10.4
    container_name: paper-editor-elasticsearch
    restart: always
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms256m -Xmx256m"
      - bootstrap.memory_lock=false
      - cluster.routing.allocation.disk.threshold_enabled=false
      - http.host=0.0.0.0
      - transport.host=localhost
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health?wait_for_status=yellow&timeout=10s || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 120s
    networks:
      - paper-editor-network

  # Kibana (Elasticsearch 管理界面)
  kibana:
    image: kibana:8.10.4
    container_name: paper-editor-kibana
    restart: always
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
    ports:
      - "5601:5601"
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - paper-editor-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: paper-editor-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - paper-editor-network

  # MinIO 对象存储 (用于文件存储)
  minio:
    image: minio/minio:RELEASE.2023-09-30T07-02-29Z
    container_name: paper-editor-minio
    restart: always
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - paper-editor-network

  # 初始化服务 (用于初始化 Elasticsearch 索引和 MinIO 存储桶)
  init-service:
    image: curlimages/curl:8.1.2
    container_name: paper-editor-init
    depends_on:
      elasticsearch:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - ./etc/elasticsearch.yaml:/app/elasticsearch.yaml
    command: >
      sh -c "
        # 等待 Elasticsearch 准备就绪
        echo 'Waiting for Elasticsearch...'
        until curl -s http://elasticsearch:9200/_cluster/health | grep -q '\"status\":\"green\"\\|\"status\":\"yellow\"'; do
          sleep 5
        done
        
        # 创建 Elasticsearch 索引 (如果不存在)
        echo 'Creating Elasticsearch index...'
        if ! curl -s -f http://elasticsearch:9200/knowledge_vectors > /dev/null 2>&1; then
          curl -X PUT 'http://elasticsearch:9200/knowledge_vectors' -H 'Content-Type: application/json' -d '{
            \"settings\": {
              \"number_of_shards\": 1,
              \"number_of_replicas\": 1,
              \"analysis\": {
                \"analyzer\": {
                  \"text_analyzer\": {
                    \"type\": \"custom\",
                    \"tokenizer\": \"standard\",
                    \"filter\": [\"lowercase\", \"asciifolding\"]
                  }
                }
              }
            },
            \"mappings\": {
              \"properties\": {
                \"knowledge_id\": {
                  \"type\": \"keyword\"
                },
                \"chunk_index\": {
                  \"type\": \"integer\"
                },
                \"chunk_content\": {
                  \"type\": \"text\",
                  \"analyzer\": \"text_analyzer\"
                },
                \"embedding\": {
                  \"type\": \"dense_vector\",
                  \"dims\": 1536,
                  \"index\": true,
                  \"similarity\": \"cosine\"
                },
                \"create_time\": {
                  \"type\": \"date\"
                },
                \"update_time\": {
                  \"type\": \"date\"
                }
              }
            }
          }'
          echo 'Elasticsearch index created successfully.'
        else
          echo 'Elasticsearch index already exists, skipping creation.'
        fi
        
        # 等待 MinIO 准备就绪
        echo 'Waiting for MinIO...'
        until curl -s http://minio:9000/minio/health/live; do
          sleep 5
        done
        
        # 创建 MinIO 存储桶 (需要 MinIO 客户端)
        echo 'MinIO is ready. Use MinIO Console to create buckets.'
      "
    networks:
      - paper-editor-network

volumes:
  postgres_data:
  elasticsearch_data:
  redis_data:
  minio_data:

networks:
  paper-editor-network:
    driver: bridge