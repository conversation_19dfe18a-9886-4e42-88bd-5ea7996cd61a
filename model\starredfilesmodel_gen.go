// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	starredFilesFieldNames          = builder.RawFieldNames(&StarredFiles{}, true)
	starredFilesRows                = strings.Join(starredFilesFieldNames, ",")
	starredFilesRowsExpectAutoSet   = strings.Join(stringx.Remove(starredFilesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	starredFilesRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(starredFilesFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	starredFilesModel interface {
		Insert(ctx context.Context, data *StarredFiles) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*StarredFiles, error)
		FindOneByFileIdUserId(ctx context.Context, fileId string, userId int64) (*StarredFiles, error)
		Update(ctx context.Context, data *StarredFiles) error
		Delete(ctx context.Context, id int64) error
	}

	defaultStarredFilesModel struct {
		conn  sqlx.SqlConn
		table string
	}

	StarredFiles struct {
		Id         int64     `db:"id"`
		FileId     string    `db:"file_id"`
		UserId     int64     `db:"user_id"`
		CreateTime time.Time `db:"create_time"`
	}
)

func newStarredFilesModel(conn sqlx.SqlConn) *defaultStarredFilesModel {
	return &defaultStarredFilesModel{
		conn:  conn,
		table: `"public"."starred_files"`,
	}
}

func (m *defaultStarredFilesModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultStarredFilesModel) FindOne(ctx context.Context, id int64) (*StarredFiles, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", starredFilesRows, m.table)
	var resp StarredFiles
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStarredFilesModel) FindOneByFileIdUserId(ctx context.Context, fileId string, userId int64) (*StarredFiles, error) {
	var resp StarredFiles
	query := fmt.Sprintf("select %s from %s where file_id = $1 and user_id = $2 limit 1", starredFilesRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, fileId, userId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStarredFilesModel) Insert(ctx context.Context, data *StarredFiles) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2)", m.table, starredFilesRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.FileId, data.UserId)
	return ret, err
}

func (m *defaultStarredFilesModel) Update(ctx context.Context, newData *StarredFiles) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, starredFilesRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.FileId, newData.UserId)
	return err
}

func (m *defaultStarredFilesModel) tableName() string {
	return m.table
}
