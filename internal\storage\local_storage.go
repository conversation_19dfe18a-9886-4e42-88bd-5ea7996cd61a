package storage

import (
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
)

// 本地存储实现
type LocalStorage struct {
	basePath string
}

// 创建本地存储
func NewLocalStorage(basePath string) *LocalStorage {
	// 确保基础路径存在
	if _, err := os.Stat(basePath); os.IsNotExist(err) {
		os.MkdirAll(basePath, 0755)
	}

	return &LocalStorage{
		basePath: basePath,
	}
}

// 保存文件
func (s *LocalStorage) SaveFile(file *multipart.FileHeader, path string) (string, error) {
	// 确保目录存在
	dir := filepath.Dir(filepath.Join(s.basePath, path))
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return "", fmt.Errorf("创建目录失败: %w", err)
		}
	}

	// 打开源文件
	src, err := file.Open()
	if err != nil {
		return "", fmt.Errorf("打开源文件失败: %w", err)
	}
	defer src.Close()

	// 创建目标文件
	dst, err := os.Create(filepath.Join(s.basePath, path))
	if err != nil {
		return "", fmt.Errorf("创建目标文件失败: %w", err)
	}
	defer dst.Close()

	// 复制文件内容
	if _, err = io.Copy(dst, src); err != nil {
		return "", fmt.Errorf("复制文件内容失败: %w", err)
	}

	return path, nil
}

// 获取文件
func (s *LocalStorage) GetFile(path string) (io.ReadCloser, error) {
	file, err := os.Open(filepath.Join(s.basePath, path))
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}

	return file, nil
}

// 删除文件
func (s *LocalStorage) DeleteFile(path string) error {
	err := os.Remove(filepath.Join(s.basePath, path))
	if err != nil {
		return fmt.Errorf("删除文件失败: %w", err)
	}

	return nil
}

// 生成访问URL
func (s *LocalStorage) GenerateURL(path string, expire int64) (string, error) {
	// 本地存储不支持生成带过期时间的URL
	// 这里简单返回文件路径
	if _, err := os.Stat(filepath.Join(s.basePath, path)); os.IsNotExist(err) {
		return "", errors.New("文件不存在")
	}

	return "/files/" + path, nil
}
