package team

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMyTeamsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取我的团队列表
func NewGetMyTeamsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMyTeamsLogic {
	return &GetMyTeamsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMyTeamsLogic) GetMyTeams() (resp []types.TeamInfo, err error) {
	// todo: add your logic here and delete this line

	return
}
