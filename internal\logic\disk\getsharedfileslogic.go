package disk

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSharedFilesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取共享文件列表
func NewGetSharedFilesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSharedFilesLogic {
	return &GetSharedFilesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSharedFilesLogic) GetSharedFiles(req *types.FileListReq) (resp *types.FileListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
