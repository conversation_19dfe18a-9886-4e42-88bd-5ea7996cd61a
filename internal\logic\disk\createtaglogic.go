package disk

import (
	"context"
	"errors"
	"fmt"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/model"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建标签
func NewCreateTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateTagLogic {
	return &CreateTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// CreateTag 创建标签
// @param req *types.CreateTagReq 标签创建请求参数，包含标签名称和颜色
// @return *types.CreateTagResp 标签创建响应，包含创建成功的标签ID
// @return error 错误信息，创建失败时返回具体错误原因
func (l *CreateTagLogic) CreateTag(req *types.CreateTagReq) (*types.CreateTagResp, error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 检查标签名称是否已存在
	existingTag, err := l.svcCtx.FileTagsModel.FindByTagName(l.ctx, req.TagName, userId)
	if err != nil && err != model.ErrNotFound {
		return nil, fmt.Errorf("检查标签是否存在失败: %w", err)
	}
	if existingTag != nil {
		return nil, errors.New("标签名称已存在")
	}

	// 设置默认颜色
	color := req.Color
	if color == "" {
		color = "#1890ff"
	}

	// 创建标签记录
	tagModel := &model.FileTags{
		TagName: req.TagName,
		Color:   color,
		UserId:  userId,
	}

	result, err := l.svcCtx.FileTagsModel.Insert(l.ctx, tagModel)
	if err != nil {
		return nil, fmt.Errorf("创建标签失败: %w", err)
	}

	// 获取插入的ID
	tagId, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("获取标签ID失败: %w", err)
	}

	return &types.CreateTagResp{
		TagId: tagId,
	}, nil
}
