package disk

import (
	"net/http"
	"paper-editor-api/response"

	"github.com/zeromicro/go-zero/rest/httpx"
	"paper-editor-api/internal/logic/disk"
	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"
)

// 获取回收站文件列表
func GetTrashFilesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.FileListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := disk.NewGetTrashFilesLogic(r.Context(), svcCtx)
		resp, err := l.GetTrashFiles(&req)
		response.Response(w, resp, err)
	}
}
