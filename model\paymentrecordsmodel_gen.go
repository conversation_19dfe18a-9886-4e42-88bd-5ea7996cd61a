// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	paymentRecordsFieldNames          = builder.RawFieldNames(&PaymentRecords{}, true)
	paymentRecordsRows                = strings.Join(paymentRecordsFieldNames, ",")
	paymentRecordsRowsExpectAutoSet   = strings.Join(stringx.Remove(paymentRecordsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	paymentRecordsRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(paymentRecordsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	paymentRecordsModel interface {
		Insert(ctx context.Context, data *PaymentRecords) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PaymentRecords, error)
		Update(ctx context.Context, data *PaymentRecords) error
		Delete(ctx context.Context, id int64) error
	}

	defaultPaymentRecordsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	PaymentRecords struct {
		Id            int64          `db:"id"`
		OrderId       string         `db:"order_id"`
		TransactionId sql.NullString `db:"transaction_id"`
		Amount        float64        `db:"amount"`
		Status        int64          `db:"status"`
		PaymentMethod string         `db:"payment_method"`
		PaymentTime   time.Time      `db:"payment_time"`
	}
)

func newPaymentRecordsModel(conn sqlx.SqlConn) *defaultPaymentRecordsModel {
	return &defaultPaymentRecordsModel{
		conn:  conn,
		table: `"public"."payment_records"`,
	}
}

func (m *defaultPaymentRecordsModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultPaymentRecordsModel) FindOne(ctx context.Context, id int64) (*PaymentRecords, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", paymentRecordsRows, m.table)
	var resp PaymentRecords
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPaymentRecordsModel) Insert(ctx context.Context, data *PaymentRecords) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6)", m.table, paymentRecordsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.OrderId, data.TransactionId, data.Amount, data.Status, data.PaymentMethod, data.PaymentTime)
	return ret, err
}

func (m *defaultPaymentRecordsModel) Update(ctx context.Context, data *PaymentRecords) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, paymentRecordsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Id, data.OrderId, data.TransactionId, data.Amount, data.Status, data.PaymentMethod, data.PaymentTime)
	return err
}

func (m *defaultPaymentRecordsModel) tableName() string {
	return m.table
}
