package team

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type LeaveTeamLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 退出团队
func NewLeaveTeamLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LeaveTeamLogic {
	return &LeaveTeamLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LeaveTeamLogic) LeaveTeam() (resp *types.BaseResp, err error) {
	// todo: add your logic here and delete this line

	return
}
