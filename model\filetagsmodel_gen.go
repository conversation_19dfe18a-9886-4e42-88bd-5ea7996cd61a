// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	fileTagsFieldNames          = builder.RawFieldNames(&FileTags{}, true)
	fileTagsRows                = strings.Join(fileTagsFieldNames, ",")
	fileTagsRowsExpectAutoSet   = strings.Join(stringx.Remove(fileTagsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	fileTagsRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(fileTagsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	fileTagsModel interface {
		Insert(ctx context.Context, data *FileTags) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*FileTags, error)
		FindOneByTagNameUserId(ctx context.Context, tagName string, userId int64) (*FileTags, error)
		Update(ctx context.Context, data *FileTags) error
		Delete(ctx context.Context, id int64) error
	}

	defaultFileTagsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	FileTags struct {
		Id         int64     `db:"id"`
		TagName    string    `db:"tag_name"`
		Color      string    `db:"color"`
		UserId     int64     `db:"user_id"`
		CreateTime time.Time `db:"create_time"`
		UpdateTime time.Time `db:"update_time"`
	}
)

func newFileTagsModel(conn sqlx.SqlConn) *defaultFileTagsModel {
	return &defaultFileTagsModel{
		conn:  conn,
		table: `"public"."file_tags"`,
	}
}

func (m *defaultFileTagsModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultFileTagsModel) FindOne(ctx context.Context, id int64) (*FileTags, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", fileTagsRows, m.table)
	var resp FileTags
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFileTagsModel) FindOneByTagNameUserId(ctx context.Context, tagName string, userId int64) (*FileTags, error) {
	var resp FileTags
	query := fmt.Sprintf("select %s from %s where tag_name = $1 and user_id = $2 limit 1", fileTagsRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, tagName, userId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultFileTagsModel) Insert(ctx context.Context, data *FileTags) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3)", m.table, fileTagsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.TagName, data.Color, data.UserId)
	return ret, err
}

func (m *defaultFileTagsModel) Update(ctx context.Context, newData *FileTags) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, fileTagsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.TagName, newData.Color, newData.UserId)
	return err
}

func (m *defaultFileTagsModel) tableName() string {
	return m.table
}
