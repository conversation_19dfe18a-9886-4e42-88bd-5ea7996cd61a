package disk

import (
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/disk"
	"paper-editor-api/internal/svc"
)

// 获取文件详情
func GetFileDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := disk.NewGetFileDetailLogic(r.Context(), svcCtx)
		resp, err := l.GetFileDetail()
		response.Response(w, resp, err)
	}
}
