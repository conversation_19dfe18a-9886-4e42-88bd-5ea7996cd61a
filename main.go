package main

import (
	"flag"
	"fmt"
	"net/http"

	"paper-editor-api/internal/config"
	"paper-editor-api/internal/handler"
	"paper-editor-api/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/editor-api.yaml", "配置文件路径")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()

	// 添加静态文件服务
	server.AddRoute(rest.Route{
		Method: http.MethodGet,
		Path:   "/docs",
		Handler: func(w http.ResponseWriter, r *http.Request) {
			http.ServeFile(w, r, "./swagger/index.html")
		},
	})

	server.AddRoute(rest.Route{
		Method: http.MethodGet,
		Path:   "/docs/swagger.json",
		Handler: func(w http.ResponseWriter, r *http.Request) {
			http.ServeFile(w, r, "./swagger/swagger.json")
		},
	})

	// 创建服务上下文
	ctx := svc.NewServiceContext(c)

	// 注册路由
	handler.RegisterHandlers(server, ctx)

	fmt.Printf("启动服务器在 %s:%d...\n", c.Host, c.Port)
	server.Start()
}
