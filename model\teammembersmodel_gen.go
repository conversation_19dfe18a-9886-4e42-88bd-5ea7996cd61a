// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.5

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	teamMembersFieldNames          = builder.RawFieldNames(&TeamMembers{}, true)
	teamMembersRows                = strings.Join(teamMembersFieldNames, ",")
	teamMembersRowsExpectAutoSet   = strings.Join(stringx.Remove(teamMembersFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	teamMembersRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(teamMembersFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	teamMembersModel interface {
		Insert(ctx context.Context, data *TeamMembers) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*TeamMembers, error)
		FindOneByTeamIdUserId(ctx context.Context, teamId string, userId int64) (*TeamMembers, error)
		Update(ctx context.Context, data *TeamMembers) error
		Delete(ctx context.Context, id int64) error
	}

	defaultTeamMembersModel struct {
		conn  sqlx.SqlConn
		table string
	}

	TeamMembers struct {
		Id       int64     `db:"id"`
		TeamId   string    `db:"team_id"`
		UserId   int64     `db:"user_id"`
		Role     string    `db:"role"`
		Status   int64     `db:"status"`
		JoinTime time.Time `db:"join_time"`
	}
)

func newTeamMembersModel(conn sqlx.SqlConn) *defaultTeamMembersModel {
	return &defaultTeamMembersModel{
		conn:  conn,
		table: `"public"."team_members"`,
	}
}

func (m *defaultTeamMembersModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultTeamMembersModel) FindOne(ctx context.Context, id int64) (*TeamMembers, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", teamMembersRows, m.table)
	var resp TeamMembers
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTeamMembersModel) FindOneByTeamIdUserId(ctx context.Context, teamId string, userId int64) (*TeamMembers, error) {
	var resp TeamMembers
	query := fmt.Sprintf("select %s from %s where team_id = $1 and user_id = $2 limit 1", teamMembersRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, teamId, userId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTeamMembersModel) Insert(ctx context.Context, data *TeamMembers) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5)", m.table, teamMembersRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.TeamId, data.UserId, data.Role, data.Status, data.JoinTime)
	return ret, err
}

func (m *defaultTeamMembersModel) Update(ctx context.Context, newData *TeamMembers) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, teamMembersRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.TeamId, newData.UserId, newData.Role, newData.Status, newData.JoinTime)
	return err
}

func (m *defaultTeamMembersModel) tableName() string {
	return m.table
}
