package model

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ FilesModel = (*customFilesModel)(nil)

type (
	// FilesModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFilesModel.
	FilesModel interface {
		filesModel
		withSession(session sqlx.Session) FilesModel
		FindTrashByUserId(ctx context.Context, userId int64, page, pageSize int) ([]*Files, error)
		CountTrashByUserId(ctx context.Context, userId int64) (int64, error)
		FindAllByFolderId(ctx context.Context, userId int64, folderId string, page, pageSize int) ([]*Files, error)
		CountByFolderId(ctx context.Context, userId int64, folderId string) (int64, error)
		FindByTag(ctx context.Context, userId int64, tagId int64, page, pageSize int) ([]*Files, error)
		CountByTag(ctx context.Context, userId int64, tagId int64) (int64, error)
	}

	customFilesModel struct {
		*defaultFilesModel
	}
)

// NewFilesModel returns a model for the database table.
func NewFilesModel(conn sqlx.SqlConn) FilesModel {
	return &customFilesModel{
		defaultFilesModel: newFilesModel(conn),
	}
}

func (m *customFilesModel) withSession(session sqlx.Session) FilesModel {
	return NewFilesModel(sqlx.NewSqlConnFromSession(session))
}

// FindTrashByUserId 查询用户回收站中的文件
func (m *customFilesModel) FindTrashByUserId(ctx context.Context, userId int64, page, pageSize int) ([]*Files, error) {
	offset := (page - 1) * pageSize
	query := fmt.Sprintf("select %s from %s where `user_id` = ? and `status` = 2 order by `update_time` desc limit ?, ?", filesRows, m.table)
	var resp []*Files
	err := m.conn.QueryRowsCtx(ctx, &resp, query, userId, offset, pageSize)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

// CountTrashByUserId 统计用户回收站中的文件数量
func (m *customFilesModel) CountTrashByUserId(ctx context.Context, userId int64) (int64, error) {
	query := fmt.Sprintf("select count(*) from %s where `user_id` = ? and `status` = 2", m.table)
	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, userId)
	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *customFilesModel) FindAllByFolderId(ctx context.Context, userId int64, folderId string, page, pageSize int) ([]*Files, error) {
	offset := (page - 1) * pageSize
	query := fmt.Sprintf("select %s from %s where `user_id` = ? and `folder_id` = ? and `status` = 1 order by `update_time` desc limit ?, ?", filesRows, m.table)
	var resp []*Files
	err := m.conn.QueryRowsCtx(ctx, &resp, query, userId, folderId, offset, pageSize)
	return resp, err
}

func (m *customFilesModel) CountByFolderId(ctx context.Context, userId int64, folderId string) (int64, error) {
	query := fmt.Sprintf("select count(*) from %s where `user_id` = ? and `folder_id` = ? and `status` = 1", m.table)
	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, userId, folderId)
	return count, err
}

func (m *customFilesModel) FindByTag(ctx context.Context, userId int64, tagId int64, page, pageSize int) ([]*Files, error) {
	offset := (page - 1) * pageSize
	query := fmt.Sprintf(`
		SELECT f.*
		FROM %s f
		INNER JOIN file_tag_relations r ON f.file_id = r.file_id
		WHERE f.user_id = ? AND r.tag_id = ? AND f.status = 1
		ORDER BY f.update_time DESC
		LIMIT ?, ?
	`, m.table)
	var resp []*Files
	err := m.conn.QueryRowsCtx(ctx, &resp, query, userId, tagId, offset, pageSize)
	return resp, err
}

func (m *customFilesModel) CountByTag(ctx context.Context, userId int64, tagId int64) (int64, error) {
	query := fmt.Sprintf(`
		SELECT count(*)
		FROM %s f
		INNER JOIN file_tag_relations r ON f.file_id = r.file_id
		WHERE f.user_id = ? AND r.tag_id = ? AND f.status = 1
	`, m.table)
	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, query, userId, tagId)
	return count, err
}
