// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	ordersFieldNames          = builder.RawFieldNames(&Orders{}, true)
	ordersRows                = strings.Join(ordersFieldNames, ",")
	ordersRowsExpectAutoSet   = strings.Join(stringx.Remove(ordersFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	ordersRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(ordersFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	ordersModel interface {
		Insert(ctx context.Context, data *Orders) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Orders, error)
		FindOneByOrderId(ctx context.Context, orderId string) (*Orders, error)
		Update(ctx context.Context, data *Orders) error
		Delete(ctx context.Context, id int64) error
	}

	defaultOrdersModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Orders struct {
		Id               int64          `db:"id"`
		OrderId          string         `db:"order_id"`
		UserId           int64          `db:"user_id"`
		MembershipTypeId int64          `db:"membership_type_id"`
		Amount           float64        `db:"amount"`
		Duration         int64          `db:"duration"`
		Status           int64          `db:"status"`
		PaymentMethod    sql.NullString `db:"payment_method"`
		PaymentTime      sql.NullTime   `db:"payment_time"`
		CreateTime       time.Time      `db:"create_time"`
		UpdateTime       time.Time      `db:"update_time"`
	}
)

func newOrdersModel(conn sqlx.SqlConn) *defaultOrdersModel {
	return &defaultOrdersModel{
		conn:  conn,
		table: `"public"."orders"`,
	}
}

func (m *defaultOrdersModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultOrdersModel) FindOne(ctx context.Context, id int64) (*Orders, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", ordersRows, m.table)
	var resp Orders
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultOrdersModel) FindOneByOrderId(ctx context.Context, orderId string) (*Orders, error) {
	var resp Orders
	query := fmt.Sprintf("select %s from %s where order_id = $1 limit 1", ordersRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, orderId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultOrdersModel) Insert(ctx context.Context, data *Orders) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2, $3, $4, $5, $6, $7, $8)", m.table, ordersRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.OrderId, data.UserId, data.MembershipTypeId, data.Amount, data.Duration, data.Status, data.PaymentMethod, data.PaymentTime)
	return ret, err
}

func (m *defaultOrdersModel) Update(ctx context.Context, newData *Orders) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, ordersRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.OrderId, newData.UserId, newData.MembershipTypeId, newData.Amount, newData.Duration, newData.Status, newData.PaymentMethod, newData.PaymentTime)
	return err
}

func (m *defaultOrdersModel) tableName() string {
	return m.table
}
