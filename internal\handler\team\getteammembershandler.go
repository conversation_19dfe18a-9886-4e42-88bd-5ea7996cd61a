package team

import (
	"net/http"
	"paper-editor-api/response"

	"paper-editor-api/internal/logic/team"
	"paper-editor-api/internal/svc"
)

// 获取团队成员列表
func GetTeamMembersHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := team.NewGetTeamMembersLogic(r.Context(), svcCtx)
		resp, err := l.GetTeamMembers()
		response.Response(w, resp, err)
	}
}
