// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	tagsFieldNames          = builder.RawFieldNames(&Tags{}, true)
	tagsRows                = strings.Join(tagsFieldNames, ",")
	tagsRowsExpectAutoSet   = strings.Join(stringx.Remove(tagsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"), ",")
	tagsRowsWithPlaceHolder = builder.PostgreSqlJoin(stringx.Remove(tagsFieldNames, "id", "create_at", "create_time", "created_at", "update_at", "update_time", "updated_at"))
)

type (
	tagsModel interface {
		Insert(ctx context.Context, data *Tags) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Tags, error)
		FindOneByNameUserId(ctx context.Context, name string, userId int64) (*Tags, error)
		Update(ctx context.Context, data *Tags) error
		Delete(ctx context.Context, id int64) error
	}

	defaultTagsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Tags struct {
		Id         int64     `db:"id"`
		Name       string    `db:"name"`
		UserId     int64     `db:"user_id"`
		CreateTime time.Time `db:"create_time"`
	}
)

func newTagsModel(conn sqlx.SqlConn) *defaultTagsModel {
	return &defaultTagsModel{
		conn:  conn,
		table: `"public"."tags"`,
	}
}

func (m *defaultTagsModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where id = $1", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultTagsModel) FindOne(ctx context.Context, id int64) (*Tags, error) {
	query := fmt.Sprintf("select %s from %s where id = $1 limit 1", tagsRows, m.table)
	var resp Tags
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTagsModel) FindOneByNameUserId(ctx context.Context, name string, userId int64) (*Tags, error) {
	var resp Tags
	query := fmt.Sprintf("select %s from %s where name = $1 and user_id = $2 limit 1", tagsRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, name, userId)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTagsModel) Insert(ctx context.Context, data *Tags) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values ($1, $2)", m.table, tagsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Name, data.UserId)
	return ret, err
}

func (m *defaultTagsModel) Update(ctx context.Context, newData *Tags) error {
	query := fmt.Sprintf("update %s set %s where id = $1", m.table, tagsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Id, newData.Name, newData.UserId)
	return err
}

func (m *defaultTagsModel) tableName() string {
	return m.table
}
