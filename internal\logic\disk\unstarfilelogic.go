package disk

import (
	"context"

	"paper-editor-api/internal/svc"
	"paper-editor-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UnstarFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 取消标星文件
func NewUnstarFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UnstarFileLogic {
	return &UnstarFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UnstarFileLogic) UnstarFile() (resp *types.BaseResp, err error) {
	// todo: add your logic here and delete this line

	return
}
