package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ FileAccessLogsModel = (*customFileAccessLogsModel)(nil)

type (
	// FileAccessLogsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFileAccessLogsModel.
	FileAccessLogsModel interface {
		fileAccessLogsModel
		withSession(session sqlx.Session) FileAccessLogsModel
	}

	customFileAccessLogsModel struct {
		*defaultFileAccessLogsModel
	}
)

// NewFileAccessLogsModel returns a model for the database table.
func NewFileAccessLogsModel(conn sqlx.SqlConn) FileAccessLogsModel {
	return &customFileAccessLogsModel{
		defaultFileAccessLogsModel: newFileAccessLogsModel(conn),
	}
}

func (m *customFileAccessLogsModel) withSession(session sqlx.Session) FileAccessLogsModel {
	return NewFileAccessLogsModel(sqlx.NewSqlConnFromSession(session))
}
