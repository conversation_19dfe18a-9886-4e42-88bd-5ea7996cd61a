package disk

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"
	"paper-editor-api/internal/middleware"
	"paper-editor-api/internal/svc"
)

type EmptyTrashLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 清空回收站
func NewEmptyTrashLogic(ctx context.Context, svcCtx *svc.ServiceContext) *EmptyTrashLogic {
	return &EmptyTrashLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// EmptyTrash 清空指定用户的回收站，包括文件和文件夹。
// 该方法会从上下文中获取当前用户ID，查询其回收站中的所有文件和文件夹，
// 然后逐一删除物理文件（仅文件）并从数据库中移除记录。
//
// 返回值：
//   - resp: 操作是否成功，true 表示成功清空回收站
//   - err: 如果操作过程中发生错误，则返回相应的错误信息
func (l *EmptyTrashLogic) EmptyTrash() (resp bool, err error) {
	// 从上下文中获取用户ID
	userId, ok := l.ctx.Value(middleware.UserIdKey).(int64)
	if !ok {
		return false, errors.New("获取用户信息失败")
	}

	// 查询用户回收站中的所有文件
	trashFiles, err := l.svcCtx.FilesModel.FindTrashByUserId(l.ctx, userId, 1, 1000) // 设置较大的页面大小以获取所有文件
	if err != nil {
		l.Logger.Errorf("查询回收站文件失败: %v", err)
		return false, errors.New("清空回收站失败")
	}

	// 查询用户回收站中的所有文件夹
	trashFolders, err := l.svcCtx.FoldersModel.FindTrashByUserId(l.ctx, userId, 1, 1000) // 设置较大的页面大小以获取所有文件夹
	if err != nil {
		l.Logger.Errorf("查询回收站文件夹失败: %v", err)
		return false, errors.New("清空回收站失败")
	}

	// 获取存储服务实例
	storage := l.svcCtx.Storage

	// 删除文件：先删除物理文件，再删除数据库记录
	for _, file := range trashFiles {
		// 删除物理文件
		err := storage.DeleteFile(file.Path)
		if err != nil {
			l.Logger.Errorf("删除文件 %s 失败: %v", file.Path, err)
			// 继续处理其他文件，不中断流程
		}

		// 从数据库中删除文件记录
		err = l.svcCtx.FilesModel.Delete(l.ctx, file.Id)
		if err != nil {
			l.Logger.Errorf("从数据库删除文件记录 %s 失败: %v", file.FileId, err)
			// 继续处理其他文件，不中断流程
		}
	}

	// 删除文件夹：仅需删除数据库记录
	for _, folder := range trashFolders {
		// 从数据库中删除文件夹记录
		err = l.svcCtx.FoldersModel.Delete(l.ctx, folder.Id)
		if err != nil {
			l.Logger.Errorf("从数据库删除文件夹记录 %s 失败: %v", folder.FolderId, err)
			// 继续处理其他文件夹，不中断流程
		}
	}

	return true, nil
}
